---
description: 
globs: 
alwaysApply: true
---

## Coding Standards
- JavaScript: `"use strict";` should be used in all JavaScript files. The module pattern (defining a `module` variable and attaching functions to it) is preferred for services and controllers.
- Naming Conventions: Follow consistent naming conventions for variables, functions, and files.
- Commenting: Use clear and concise comments to explain the purpose of code sections and functions.
- Error Handling: Implement proper error handling to prevent unexpected behavior.
- When generating the code, output no more than 500-600 lines of code and make sure the functions or features can be seprated and tested


### Folder Structures
- When documenting technical details that involve folder structures, always use ASCII art
- This makes it easier for developers to understand the hierarchy and organization
- Use the following format:
```
project/
├── folder1/
│   ├── subfolder1/
│   │   └── file1.php
│   └── file2.php
├── folder2/
│   └── file3.php
└── index.php
```
- Indent each level with 4 spaces
- Use │ for vertical lines, ├── for branches, and └── for the last item in a directory
- Always include the trailing slash (/) for directories to distinguish them from files