<?php
require_once(dirname(__FILE__) . '/../fpdi/formsheet.php');
class AssessmentRequestForm extends Formsheet{
	protected static $_width = 8.5;
	protected static $_height = 11;
	protected static $_unit = 'in';
	protected static $_orient = 'P';

	protected $data;
	protected $showLines = false;

	function AssessmentRequestForm($data=null){
		$this->data = $data;
		$this->FPDF(AssessmentRequestForm::$_orient, AssessmentRequestForm::$_unit, array(AssessmentRequestForm::$_width, AssessmentRequestForm::$_height));
		$this->createSheet();
		$this->generateForm();
	}

	function generateForm(){
		// Set page format and margins
		$this->showLines = false;

		// Get payment schedule count to determine layout
		$paymentSchedules = array();
		if(isset($this->data['AssessmentPaysched']) && is_array($this->data['AssessmentPaysched'])){
			$paymentSchedules = $this->data['AssessmentPaysched'];
		}
		$paymentCount = count($paymentSchedules);

		// Generate the header section
		$this->generateHeader();

		// Generate student information section
		$this->generateStudentInfo();

		// Generate fee breakdown and discount details side by side
		$this->generateFeeAndDiscountSideBySide();
		$this->generatePaymentSchedule();
			$this->generateFooter();
		// For monthly payment plans with many entries, use a second page
		if($paymentCount > 4 &&0){
			// Generate footer on first page
			$this->generateFooter();

			// Add a new page for payment schedule
			$this->AddPage('P', 'Letter');

			// Add header to second page
			$this->generateHeader();

			// Generate payment schedule on second page with more space
			$this->generatePaymentSchedule();

			// Generate footer on second page
			$this->generateFooter();
		} else {
			// For simpler payment plans, keep everything on one page

		}
	}

	function generateHeader(){
		// Header section metrics
		$metrics = array(
			'base_x' => 0.5,
			'base_y' => 0.5,
			'width' => 7.5,
			'height' => 1.2,
			'cols' => 30,
			'rows' => 6,
		);
		$this->section($metrics);

		// School Header Image
		// Fallback to logo if header image doesn't exist
		$logoPath = dirname(__FILE__) . '/images/cjps-logo.png';
		if(file_exists($logoPath)){
			$this->DrawImage(5, 0, 0.8, 0.79, $logoPath);

			// School Name and Address
			$this->GRID['font_size'] = 14;
			$this->centerText(6, 1, "CHILD JESUS OF PRAGUE SCHOOL", 20, 'B');
			$this->GRID['font_size'] = 10;
			$this->centerText(6, 2, "999 National Road, Barangay Calumpang, Binangonan, Rizal", 20);
			$this->centerText(6, 3, "Tel: (02) 8652-0403 | Email: <EMAIL>", 20);
		}

		// Form Title
		$this->GRID['font_size'] = 14;
		$this->leftText(0, 6, "ASSESSMENT OF FEES", 30, 'B');

		// Reference Number
		if(isset($this->data['Assessment']['id'])){
			$this->GRID['font_size'] = 10;
			$this->rightText(0, 5, "Assessment No:", 30, '');
			$this->rightText(0, 6,  $this->data['Assessment']['id'], 30, 'B');
		}
	}

	function generateStudentInfo(){
		// Student Info section metrics
		$metrics = array(
			'base_x' => 0.5,
			'base_y' => 2.0,
			'width' => 7.5,
			'height' => 1.0,
			'cols' => 30,
			'rows' => 5,
			'border' => true
		);
		$this->section($metrics);

		// Section Title
		$this->GRID['font_size'] = 11;
		$this->leftText(1, 1, "STUDENT INFORMATION", 28, 'B');

		// Student Details
		$this->GRID['font_size'] = 10;

		// Get student name from account_details JSON
		$studentName = "";
		$yearLevel = "";
		$paymentPlan = "";

		if(isset($this->data['Assessment']['account_details'])){
			$accountDetails = json_decode($this->data['Assessment']['account_details'], true);
			if($accountDetails){
				$studentName = isset($accountDetails['student_name']) ? $accountDetails['student_name'] : "";
				$yearLevel = isset($accountDetails['year_level']) ? $accountDetails['year_level'] : "";
				$paymentPlan = isset($accountDetails['payment_plan']) ? $accountDetails['payment_plan'] : "";

				$studentName = utf8_decode(str_replace('NEW','',$studentName));
			}
		}

		// Student ID/Registration Number
		$regNo = isset($this->data['Assessment']['ref_no']) ? $this->data['Assessment']['ref_no'] : "";

		// Display student information
		$this->leftText(1, 2, "Student Name:", 10);
		$this->leftText(11, 2, $studentName, 19, 'B');

		$this->leftText(1, 3, "Incoming Year Level:", 10);
		$this->leftText(11, 3, $yearLevel, 19);

		$this->leftText(1, 4, "Payment Plan:", 10);
		$this->leftText(11, 4, $paymentPlan, 19);
	}

	function generateFeeAndDiscountSideBySide(){
		// Get fee details from account_details JSON
		$tuitionFee = 0;
		$miscFees = 0;
		$otherFees = 0;
		$textbookAmount = 0;
		$suppliesAmount = 0;
		$reservationFee = 0;
		$totalDiscount = 0;
		$assessmentTotal = 0;
		$discountDetails = array();

		if(isset($this->data['Assessment']['account_details'])){
			$accountDetails = json_decode($this->data['Assessment']['account_details'], true);
			if($accountDetails){
				$tuitionFee = isset($accountDetails['tuition_fee']) ? $accountDetails['tuition_fee'] : 0;
				$miscFees = isset($accountDetails['miscellaneous_fees']) ? $accountDetails['miscellaneous_fees'] : 0;
				$otherFees = isset($accountDetails['other_fees']) ? $accountDetails['other_fees'] : 0;
				$textbookAmount = isset($accountDetails['textbook_amount']) ? $accountDetails['textbook_amount'] : 0;
				$suppliesAmount = isset($accountDetails['supplies_amount']) ? $accountDetails['supplies_amount'] : 0;
				$reservationFee = isset($accountDetails['reservation_fee_amount']) ? $accountDetails['reservation_fee_amount'] : 0;
				$totalDiscount = isset($accountDetails['total_discount']) ? $accountDetails['total_discount'] : 0;

				// Get discount details
				if(isset($accountDetails['discount_details']) && is_array($accountDetails['discount_details'])){
					$discountDetails = $accountDetails['discount_details'];
				}
			}
		}

		// Get assessment total from Assessment record
		if(isset($this->data['Assessment']['assessment_total'])){
			$assessmentTotal = $this->data['Assessment']['assessment_total'];
		}

		// Create a section for side-by-side display
		$metrics = array(
			'base_x' => 0.5,
			'base_y' => 3.3,
			'width' => 7.5,
			'height' => 2.5, // Reduced height to make room for payment schedule
			'cols' => 30,
			'rows' => 12,
			'border' => true
		);
		$this->section($metrics);

		// Draw vertical line to separate the two sections
		$this->DrawLine(15, 'v', array(0, 12));

		// ===== LEFT SIDE: FEE BREAKDOWN =====

		// Section Title
		$this->GRID['font_size'] = 11;
		$this->leftText(1, 1, "FEE BREAKDOWN", 13, 'B');

		// Table Headers
		$amountX = 9;

		$this->GRID['font_size'] = 10;
		$this->leftText(1, 2, "Description", 8, 'B');
		$this->rightText($amountX, 2, "Amount (PHP)", 5, 'B');

		// Draw horizontal line below headers
		$this->DrawLine(3, 'h', array(0, 15));

		// Display fee breakdown
		$row = 4;

		// Tuition Fee
		$this->leftText(1, $row, "Tuition Fee", 8);
		$this->rightText($amountX, $row, number_format($tuitionFee, 2), 5);
		$row++;

		// Miscellaneous Fees
		$this->leftText(1, $row, "Miscellaneous Fees", 8);
		$this->rightText($amountX, $row, number_format($miscFees, 2), 5);
		$row++;

		// Other Fees
		$this->leftText(1, $row, "Other Fees", 8);
		$this->rightText($amountX, $row, number_format($otherFees, 2), 5);
		$row++;

		// Subtotal (Tuition, Misc, Other)
		$subtotal = $tuitionFee + $miscFees + $otherFees;
		$this->leftText(1, $row, "Subtotal", 8, 'B');
		$this->rightText($amountX, $row, number_format($subtotal, 2), 5, 'B');
		$row+=0.5;

		// Draw horizontal line below subtotal
		$this->DrawLine($row, 'h', array(0, 15));
		$row+=1;

		// Less: Reservation Fee
		if($reservationFee > 0){
			$this->leftText(1, $row, "Less: Reservation Fee", 8);
			$this->rightText($amountX, $row, "(" . number_format($reservationFee, 2) . ")", 5);
			$row+=0.75;
		}

		// Less: Discounts
		if($totalDiscount > 0){
			$this->leftText(1, $row, "Less: Discounts/Scholarships", 8);
			$this->rightText($amountX, $row, "(" . number_format($totalDiscount, 2) . ")", 5);
			$row+=0.75;
		}

		// Add: Textbooks and Supplies
		$textbooksSuppliesTotal = $textbookAmount + $suppliesAmount;
		if($textbooksSuppliesTotal > 0){
			$this->leftText(1, $row, "Add: Textbooks and Supplies", 8);
			$this->rightText($amountX, $row, number_format($textbooksSuppliesTotal, 2), 5);
			$row+=0.75;
		}

		// Draw horizontal line before total

		

		// Total Upon Enrollment (changed from Total Assessment)
		$row += 0.75;
		$row = 11.5;
		$this->leftText(1, $row, "UPON ENROLLMENT", 8, 'B');
		$this->rightText($amountX, $row, number_format($assessmentTotal, 2), 5, 'B');

		// ===== RIGHT SIDE: DISCOUNT DETAILS =====

		// Section Title
		$this->GRID['font_size'] = 11;
		$this->leftText(16, 1, "DISCOUNT DETAILS", 13, 'B');

		// Table Headers
		$amountX = 24;
		$this->GRID['font_size'] = 10;
		$this->leftText(16, 2, "Discount Type", 6, 'B');
		$this->rightText($amountX, 2, "Amount (PHP)", 5, 'B');

		// Draw horizontal line below headers
		$this->DrawLine(3, 'h', array(15, 15));

		// Display discount details
		$discRow = 4;
		if(!empty($discountDetails)){
			foreach($discountDetails as $discount){
				$discountType = isset($discount['type']) ? $discount['type'] : "";
				$discountAmount = isset($discount['amount']) ? $discount['amount'] : 0;

				$this->leftText(16, $discRow, $discountType, 6);
				$this->rightText($amountX, $discRow, number_format($discountAmount, 2), 5);
				$discRow++;
			}
			
			$row = 11.5;
			// Total Discounts
			$this->leftText(16, $row, "TOTAL DISCOUNTS", 8, 'B');
			$this->rightText($amountX, $row, number_format($totalDiscount, 2), 5, 'B');
		} else {
			// If no discounts, show a message
			$this->centerText(16, $discRow+3, "***** No discounts applied ****", 13);
		}
	}

	function generatePaymentSchedule(){
		// Get payment schedule from AssessmentPaysched records
		$paymentSchedules = array();
		if(isset($this->data['AssessmentPaysched']) && is_array($this->data['AssessmentPaysched'])){
			$paymentSchedules = $this->data['AssessmentPaysched'];
		}

		// Calculate required height based on number of payment entries
		$numPayments = count($paymentSchedules);
		$requiredRows = $numPayments + 4; // Headers + margin
		$requiredHeight = max(1, min(3.4, $requiredRows * 0.25)); // Between 2.0 and 4.0 inches

		// Payment Schedule section metrics - moved to bottom of page
		$metrics = array(
			'base_x' => 0.5,
			'base_y' => 6.0, // Moved up to appear right after fee breakdown and discount details
			'width' => 7.5,
			'height' => $requiredHeight,
			'cols' => 30,
			'rows' => $requiredRows,
			'border' => true
		);
		$this->section($metrics);

		// Section Title
		$this->GRID['font_size'] = 11;
		$this->leftText(1, 1, "PAYMENT SCHEDULE", 28, 'B');

		// Table Headers
		$this->GRID['font_size'] = 10;
		$amountX = 21;
		$this->leftText(1, 1.75, "Due Date", 10, 'B');
		$this->leftText(11, 1.75, "Description", 10, 'B');
		$this->rightText($amountX, 2, "Amount (PHP)", 8, 'B');

		// Draw horizontal line below headers
		$this->DrawLine(2.5, 'h');

		// Display payment schedule
		$row = 3.75;
		foreach($paymentSchedules as $schedule){
			// Format due date
			$dueDate = "Upon Enrollment";
			if($schedule['bill_month'] != 'UPONNROL' && !empty($schedule['due_date'])){
				$dueDate = date('d M Y', strtotime($schedule['due_date']));
			}

			// Determine description based on transaction type
			$description = "Payment";
			if($schedule['transaction_type_id'] == 'INIPY'){
				$description = "Initial Payment";
			} else if($schedule['transaction_type_id'] == 'SBQPY'){
				$description = "Subsequent Payment";
			}

			// Display payment schedule row
			$this->leftText(1, $row, $dueDate, 10);
			$this->leftText(11, $row, $description, 10);
			$this->rightText($amountX, $row, number_format($schedule['due_amount'], 2), 8);
			$row++;
		}
	}



	function generateFooter(){
		// Footer section metrics
		$metrics = array(
			'base_x' => 0.5,
			'base_y' => 9.75, // Adjusted to appear after payment schedule
			'width' => 7.5,
			'height' => 0.7,
			'cols' => 30,
			'rows' => 4,
		);
		$this->section($metrics);

		// Add small school icon to footer
		$iconPath = dirname(__FILE__) . '/images/cjps-icon.png';
		if(file_exists($iconPath)){
			$this->DrawImage(0, 0, 0.5, 0.5, $iconPath);
		}

		// Footer text
		$this->GRID['font_size'] = 8;
		$this->centerText(0, 1, "This is an official assessment of fees for School Year 2025-2026.", 30);
		$this->centerText(0, 2, "For questions or concerns, please contact the Business Office at (02) 8652-0403 <NAME_EMAIL>", 30);
		$this->centerText(0, 3, "Generated on: " . date('F d, Y'), 30);
	}
}
?>
