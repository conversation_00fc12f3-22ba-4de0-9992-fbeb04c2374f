"use strict";
define(['app', 'policy', 'api', 'atomic/bomb'], function(app, __POLICY) {
    app.register.controller('PayOnlineController', ['$scope', '$rootScope', '$filter', '$timeout', '$location', '$window', 'api', 'Atomic', 'aModal',
    function($scope, $rootScope, $filter, $timeout, $location, $window, api, atomic, aModal) {
        const $selfScope = $scope;
        $scope = this;

        $scope.init = function() {
            // Initialize variables
            $scope.resetForm();

            // Payment mode options
            $scope.PayModeOptions = [
                {id:'GCASH', name:'GCash'},
                {id:'PMAYA', name:'Pay Maya'},
                {id:'BANKT', name:'Bank Transfer'},
                {id:'ONSIT', name:'On-Site Payment'}
            ];

            // Check for ref_no in URL parameters
            $scope.checkUrlParams();
        };

        // Function to check URL parameters and auto-load assessment if ref_no is present
        $scope.checkUrlParams = function() {
            // Get URL parameters
            var params = $location.search();

            // If ref_no parameter exists, auto-load the assessment
            if (params.ref_no) {
                $scope.assessmentRefNo = params.ref_no;
                // Use timeout to ensure the DOM is ready
                 $scope.lookupAssessment(1500);

            }
        };

        $scope.lookupAssessment = function(delay = 0) {
            if (!$scope.assessmentRefNo) {
                $scope.errorMessage = 'Please enter an assessment reference number.';
                return;
            }

            // Validate format (should start with CJA)
            if (!$scope.assessmentRefNo.toUpperCase().startsWith('CJA')) {
                $scope.errorMessage = 'Invalid assessment reference number format. It should start with CJA.';
                return;
            }

            $scope.isLoading = true;
            $scope.errorMessage = '';

            let success = function(response) {
                
                // Show loading state for a minimum time for better UX
                $timeout(function() {
                    
                    if (response && response.data && response.data.length > 0) {
                        // Extract the data from the assessment response
                        var AObj = response.data[0];
                        var accountDetails = {};
                        
                        try {
                            // Parse account_details JSON if it exists
                            if (AObj.account_details) {
                                accountDetails = JSON.parse(AObj.account_details);
                            }
                        } catch (e) {
                            console.error('Error parsing account details:', e);
                        }

                        // Format the data to match what the UI expects
                        $scope.assessment = {
                            ref_no: AObj.id,
                            student_id: AObj.student_id,
                            student_name: accountDetails.student_name || 'N/A',
                            year_level: accountDetails.year_level || 'N/A',
                            payment_plan: accountDetails.payment_plan || 'N/A',
                            assessment_total: AObj.assessment_total,
                            token:AObj.token
                        };
                        $scope.PaidAmount = AObj.assessment_total;
                        $scope.isAssessmentLoaded = true;
                        $scope.isLoading = false;
                        
                    } else {
                        $scope.errorMessage = 'No assessment found with the provided reference number.';
                        $scope.isLoading = false;
                    }
                    scrollToTarget('assessment-details-container');
                }, 500); // Show loading state for at least 1 second
            };

            let error = function(response) {
                // Show loading state for a minimum time for better UX
                $timeout(function() {
                    $scope.isLoading = false;
                    $scope.errorMessage = 'Error retrieving assessment details. Please try again.';
                    console.error('Assessment lookup error:', response);
                }, 1000);
            };
            // Call API to get assessment details
            $timeout(function() {
                api.GET('assessments', { id: $scope.assessmentRefNo }, success, error);
            }, delay);


        };

        $scope.generatePaymentLink = function() {
            $scope.isGenerating = true;

            let success = function(response) {
                // Show processing state for a minimum time for better UX
                $timeout(function() {
                    if (response.data && response.data.checkoutUrl) {
                        // Redirect to PayMaya checkout URL
                        $window.location.href = response.data.checkoutUrl;
                    } else {
                        $scope.isGenerating = false;
                        alert('Payment Error', 'Unable to generate payment link. Please try again later.');
                    }
                }, 1500);
            };

            let error = function(response) {
                // Show processing state for a minimum time for better UX
                $timeout(function() {
                    $scope.isProcessing = false;
                    alert('Payment Error', 'Failed to process payment request. Please try again later.');
                    console.error('Payment link generation error:', response);
                }, 1500);
            };

            // Call API to generate payment link
            api.POST('online_payments/generate_link', {
                assessment_id: $scope.assessment.ref_no,
                amount: $scope.assessment.assessment_total
            }, success, error);
        };

        $scope.resetForm = function() {
            $scope.assessmentRefNo = null;
            $scope.isLoading = false;
            $scope.isProcessing = false;
            $scope.isAssessmentLoaded = false;
            $scope.errorMessage = null;
            $scope.assessment = {};
            $scope.PaymentMode = null;
            $scope.ProofOfPayment = undefined;
            $scope.PaidAmount = 0;
            $scope.showProofUpload = false;
        };

        $scope.toggleProofUpload = function() {
            $scope.showProofUpload = !$scope.showProofUpload;
            if($scope.showProofUpload)
                scrollToTarget('payment-details-container');
            else
                scrollToTarget('assessment-details-container');
        };

        $selfScope.$watchGroup(['POC.PaymentMode','POC.ProofOfPayment'],function(validations){
            
            $scope.isPaymentValid = validations[0] && validations[1]!=undefined;
            console.log(validations,$scope.isPaymentValid);
        });
        $scope.submitPaymentProof = function() {
            $scope.isProcessing = true;
            let AObj =  $scope.assessment;
            let OPYObj = {
                ref_no:AObj.ref_no,
                student_id:AObj.student_id,
                amount:$scope.PaidAmount,
                payment_method:$scope.PaymentMode,
                token:AObj.token
            };

            api.POST('online_payments',OPYObj,uploadAttachment,errorOnlinePayment);
        };
        function errorOnlinePayment(response){
            $scope.isProcessing = false;
            let errorMessage = `Error: ${response.message} Please try again.`;
            alert(errorMessage);
            
        }
        function uploadAttachment(response){
            let paymentId =  response.data.id;
            // Prepare metadata for the file upload
            let meta = {
                payment_id: paymentId
            };

            // Broadcast event to trigger file upload
            $selfScope.$broadcast('FileUploadStart', meta);

            // Watch for upload completion
            $selfScope.$watch('POC.ProofOfPayment.success', function(isUploaded) {
                if (isUploaded) {
                    $timeout(function() {
                        $scope.isProcessing = false;
                        $scope.showProofUpload = false;
                        let successURL = '#/pages/success?ref_no='+paymentId+'&type=PY1';
                        $window.location.href=successURL;
                    }, 1500);
                } else if (isUploaded === false) {
                    // Upload failed
                    $timeout(function() {
                        $scope.isProcessing = false;
                        aModal.error('Upload Failed', 'Failed to upload your proof of payment. Please try again or contact support if the problem persists.');
                    }, 1500);
                }
            });

        
        }

        function scrollToTarget(elemId){
            $timeout(function(){
                // Find the list-group-canvas element which is the scrollable container
                const listGroupCanvas = document.querySelector('.list-group-canvas');
                const target = document.getElementById(elemId);
                
                
                if (listGroupCanvas && target ) {
                    // Calculate the position of the target element relative to the list-group-canvas
                    const targetPosition = target.offsetTop +(target.offsetHeight/2);

                    // Smoothly scroll the list-group-canvas to the target position
                    listGroupCanvas.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                } else if (target) {
                    // Fallback to window scrolling if list-group-canvas is not found
                    window.scrollTo({
                        top: target.offsetTop,
                        behavior: 'smooth'
                    });
                }
            }, 400);
        }
    }]);
});