<div class="table-entry">
	<div class="table-wrapper table-entry-header" style="position:relative;z-index:3;overflow-x: hidden;">
		<table class="table table-bordered" style="margin-bottom:0px;">
			<thead>
				<tr ng-if="!autoBind && AllowAdd">
					<td colspan="{{Headers.length}}">
						<div class="help-text">
						Click
						<a-button size="xs" ng-disabled="true">
							<a-glyph icon="plus"></a-glyph>
						</a-button>
						to add an item.
						</div>
					</td>
					<th>
						<a-button type="primary" ng-click="confirmEdit()">
							<a-glyph icon="floppy-disk"></a-glyph>
						</a-button>
					</th>
				</tr>
				<tr>
					<th class="align-middle {{hdr.class}}" ng-repeat="hdr in Headers track by $index" >
						{{hdr.label?hdr.label:hdr}}
					</th>
					<th ng-class="{'col-md-1':!AllowAdd}" ng-show="AllowAdd || AllowDelete">
						<a-button ng-click="initSort()" ng-disabled="!EditItems.length" ng-show="allowSort">
							<a-glyph icon="sort"></a-glyph>
						</a-button>
						<a-button ng-show="!allowAdd&& !allowSort" ng-disabled="true" style="opacity:0;">
							<a-glyph icon="plus"></a-glyph>
						</a-button>

					</th>
				</tr>
				<tr ng-if="AllowAdd">
					<td ng-if="Props.indexOf('student')>-1" colspan="{{Props.length}}">
						<a-row>
							<a-col>
								<m-search-student ng-model="NewItem.student"></m-search-student>
							</a-col>
						</a-row>
					</td>
					<td ng-if="Props.indexOf('student')==-1" class="align-middle {{Headers[$index].class}}" ng-repeat="prop in Props track by $index">
						
						<m-search-entity ng-if="Inputs[$index].search"ng-model="NewItem.subject" endpoint="Inputs[$index].search.endpoint" search-fields="Inputs[$index].search.fields" obj-fields="Inputs[$index].search.objFields" display-field="'name'" filter="Inputs[$index].search.filter" ></m-search-entity>
							
						
						<a-textbox
						ng-if="!Inputs[$index].options && !Inputs[$index].search" 
						ng-model="NewItem[Inputs[$index].field]"
						ng-disabled="Inputs[$index].disabled"
						placeholder="Inputs[$index].placeholder"
						type="Inputs[$index].type"
						></a-textbox>
						<select
						 a-select
						 	ng-if="Inputs[$index].options" 
							ng-model="NewItem[Inputs[$index].field]"
							ng-options="oItem.id as oItem[Inputs[$index].optionLabel] group by  oItem[Inputs[$index].optionsGroup] for oItem in Inputs[$index].options" class="form-control">
							<option value="">Select {{Inputs[$index].field}}</option>
						</select>
					</td>
					<td class="text-center">
						<a-button type="success" ng-click="addItem(NewItem)" ng-disabled="DisableLine=='add'">
							<a-glyph icon="plus"></a-glyph>
						</a-button>
					</td>
				</tr>
			</thead>
		</table>
	</div>
	<div class="table-wrapper table-entry-data"  style="position:relative;z-index:2;max-height:{{maxHeight}}px;overflow:auto;">
		<table class="table table-bordered" style="margin-top:{{offsetY}}px;margin-bottom:0px;">
			<thead style="border:none;opacity:0;">
				<tr>
					<th class="align-middle {{hdr.class}}" ng-repeat="hdr in Headers track by $index" >
						{{hdr.label?hdr.label:hdr}}
					</th>
					<th ng-class="{'col-md-1':!AllowAdd}" ng-show="AllowAdd || AllowDelete">
						<a-button type="success">
							<a-glyph icon="plus"></a-glyph>
						</a-button>
					</th>
				</tr>
			</thead>
			<tbody>
				<tr ng-repeat="(row,Item) in EditItems track by $index" ng-class="{'info':ActiveIndex==$index}">
					<td ng-if="Props.indexOf('student')>-1" class="align-middle {{Headers[$index].class}}" ng-repeat="prop in Props track by $index">
						<a-textbox
						ng-if="$index!=Props.indexOf('student')"
						ng-disabled="true"
						ng-model="Item.student[prop]"></a-textbox>
						<a-textbox
						ng-if="$index==Props.indexOf('student')"
						ng-disabled="true"
						ng-model="Item.student[Inputs[$index].field]"></a-textbox>
					</td>
					<td  ng-if="Props.indexOf('student')==-1" class="align-middle {{Headers[$index].class}}" ng-repeat="prop in Props track by $index">
					

						<a-textbox
						ng-if="Inputs[$index].search"
						ng-disabled="true"
						ng-model="Item[Inputs[$index].field].name"
						placeholder="Inputs[$index].placeholder"></a-textbox>

						<a-textbox
						ng-if="!Inputs[$index].options && !Inputs[$index].search"
						ng-disabled="Inputs[$index].disabled || DisableInput"
						ng-enabled="Inputs[$index].enableIf===Item.id  && Inputs[$index].enableIf"
						ng-model="Item[Inputs[$index].field]"
						placeholder="Inputs[$index].placeholder"
						type="Inputs[$index].type"></a-textbox>
						<select
							a-select
							ng-if="Inputs[$index].options" 
							ng-disabled="Inputs[$index].disabled"
							ng-model="Item[Inputs[$index].field]"
							ng-options="oItem.id as oItem[Inputs[$index].optionLabel] group by  oItem[Inputs[$index].optionsGroup] for oItem in Inputs[$index].options" class="form-control">
							<option value="">Select {{Inputs[$index].field}}</option>
						</select>
					</td>
					<td class="text-center" ng-show="AllowDelete">
						<a-button type="danger" ng-disabled="DisableLine==$index" ng-click="deleteItem($index)">
							<a-glyph icon="remove"></a-glyph>
						</a-button>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
</div>