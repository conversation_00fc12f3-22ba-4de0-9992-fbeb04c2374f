# Lapis Plus Documentation

Welcome to the Lapis Plus (Learners, Admin and Parents Information System) documentation. This documentation provides comprehensive information about the system's features, functionality, and implementation details.

## Documentation Overview

The documentation is organized into different sections based on the system's main processes and features:

### 1. Registration and Enrollment

This section covers the online registration and enrollment processes:

- **Online Registration**: How students register for admission to the school
- **Enrollment**: How registered students proceed with the enrollment process

### 2. Assessment and Fee Management

This section covers the assessment of fees and related processes:

- **Assessment**: Overview of the fee assessment process
- **Tuition Fee Management**: How tuition fees are updated and validated
- **Textbooks and Supplies**: Integration of textbooks and school supplies into the assessment
- **Reservation Fee**: How reservation fees are deducted from the total assessment
- **Discounts**: How various discounts are applied and distributed across payment schedules
- **Assessment Confirmation**: The process of confirming and finalizing assessments
- **Online Payment System**: How parents can pay fees online using PayMaya or manual payment with proof upload
- **Online Payments Monitoring**: Administrative tools for tracking and managing online payment transactions
- **Payment Proof Upload**: How to submit proof of payments made through bank transfers or other channels

### 3. Development Guidelines

This section covers technical implementation details and development standards:

- **UX Implementation Patterns**: Standard patterns for implementing consistent user experience across modules
- **API Integration**: Guidelines for integrating with the system's API endpoints
- **Code Organization**: Standards for organizing code in controllers and views

## Getting Started

To navigate the documentation:

1. Use the sidebar on the left to browse through the available documentation
2. Click on any topic to view its details
3. Follow the logical flow of topics to understand the complete process

## Need Help?

If you need further assistance or have questions about the system, please contact the system administrator or the IT department.

---

*This documentation is maintained by the LAPIS development team and is updated regularly to reflect the latest features and changes to the system.*
