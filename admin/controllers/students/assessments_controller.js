"use strict";
define(['app',
        'api',
        'atomic/bomb',
        'policy/discounts',
        'policy/pay_a_cash',
        'policy/pay_b_semestral',
        'policy/pay_c_quarterly',
        'policy/pay_d_monthly',
        'core/assessment_calculator',
        'core/ui_manager',
        'core/discount_eligibility',
        'core/reservation_manager',
        'core/assessment_request_manager',
], function (app, api, atomic, Discounts, PayACash, PayBSemestral, PayCQuarterly, PayDMonthly, AssessmentCalculator, UIManager, DiscountEligibility, ReservationManager,AssessmentRequestManager) {
    app.register.controller('AssessmentController',['$scope','$rootScope','$filter','$timeout','$http','api','Atomic','aModal',function ($scope,$rootScope,$filter,$timeout,$http,api,atomic,aModal) {
       const $selfScope =  $scope;
       const $SMC = $selfScope.$parent.SMC;
       $scope = this;

       // Add these properties to your controller
       $scope.scholarshipActiveTab = 0;

       // Fee table configuration
       $scope.ScholarshipFeeHeaders = ['Fee', 'Original Amount', 'Final Amount'];
       $scope.ScholarshipFeeProps = ['fee', 'amount', 'final_amount'];
       $scope.ScholarshipFeeData = [];
       $scope.ScholarshipFeeInputs = [
           {field: 'fee',disabled: true, options:[
               {id:'TUI', name:'Tuition Fee'},
               {id:'MSC', name:'Miscellaneous Fees'},
               {id:'OTH', name:'Other Fees'}
           ], enableIf:'OTH'},
           {field: 'amount',disabled: true},
           {field: 'final_amount', type: 'number'}
       ];

       // Schedule table configuration
       $scope.ScholarshipSchedHeaders = ['Due Date', 'Description', 'Amount'];
       $scope.ScholarshipSchedProps = ['due_date', 'description', 'amount'];
       $scope.ScholarshipSchedData = [];
       $scope.ScholarshipSchedInputs = [
           {field: 'due_date', type: 'date'},
           {field: 'description', type: 'text'},
           {field: 'amount', type: 'number'}
       ];

       // Update functions for the tables
       $scope.updateScholarshipFees = function(items) {
           $scope.ScholarshipFeeData = items;
           // Additional logic to calculate totals or update assessment
       };

       $scope.updateScholarshipSchedule = function(items) {
           $scope.ScholarshipSchedData = items;
           // Additional logic to update payment schedule
       };

       // Function to populate scholarship fee data
       $scope.populateScholarshipFees = function() {
           if (!$scope.Assessment.year_level_id || !$scope.Assessment.payment_plan_id) {
               $scope.ScholarshipFeeData = [];
               return;
           }

           // Get the current tuition data to extract fee amounts
           let yearLevelKey = $scope.Assessment.year_level_id;
           let planId = $scope.Assessment.payment_plan_id;

           // Get policy data based on payment plan
           let policyData;
           switch (planId) {
               case 'PLANA':
                   policyData = PayACash.PLAN_A_CASH_BASIS[yearLevelKey];
                   break;
               case 'PLANB':
                   policyData = PayBSemestral.PLAN_B_SEMESTRAL[yearLevelKey];
                   break;
               case 'PLANC':
                   policyData = PayCQuarterly.PLAN_C_QUARTERLY[yearLevelKey];
                   break;
               case 'PLAND':
                   policyData = PayDMonthly.PLAN_D_MONTHLY[yearLevelKey];
                   break;
           }

           if (policyData) {
               $scope.ScholarshipFeeData = [
                   {
                       fee: 'TUI',
                       amount: policyData.tuition_fee,
                       final_amount: policyData.tuition_fee
                   },
                   {
                       fee: 'MSC',
                       amount: policyData.miscellaneous_fees,
                       final_amount: policyData.miscellaneous_fees
                   },
                   {
                       fee: 'OTH',
                       amount: policyData.other_fees,
                       final_amount: policyData.other_fees
                   }
               ];
           } else {
               $scope.ScholarshipFeeData = [];
           }
       };

       $scope.init = function (module_name) {
			prepareUI();
			setUpWatchers();
			atomic.ready(function(){
				 $scope.SchoolYears = $filter('filter')(atomic.SchoolYears,{id:atomic.ActiveSY});
				 loadRequests();
				 resetAssessment();
			});
			atomic.fuse();

      }

      function prepareUI(){
		  let SchoolYears = atomic.SchoolYears;
		  let ActiveSY  = atomic.ActiveSY;
			let PaymentPlans = AssessmentCalculator.__PAYMENT_PLANS;
		 // Initialize UI components using the UI Manager
         UIManager.initUI(SchoolYears, ActiveSY, $scope,$filter);
         // Use the UI Manager to prepare the UI with DiscountEligibility
         UIManager.prepareUI($scope, Discounts, PaymentPlans, $SMC, DiscountEligibility);
         // Setup the right panel
         UIManager.setupRightPanel($selfScope, $scope, $timeout);
      }

	  function setUpWatchers(){
		  // Setup discount watchers using the DiscountEligibility & ReservationManager
		  DiscountEligibility.setupDiscountWatchers($selfScope, $scope, updateAssessmentDetails);
		  ReservationManager.setupReservationWatchers($selfScope, $scope, updateAssessmentDetails);
		  // Setup test mode watcher
		  UIManager.setupTestModeWatch($selfScope, $scope);

		  // Setup disable view requests watcher
		  UIManager.setupDisableViewRequestsWatch($selfScope, $scope);

		  // Watch for changes in SendEmailFlags to validate email selection
		  $selfScope.$watch('ASC.SendEmailFlags', function() {
			  // Validate email selection whenever flags change
			  if ($scope.validateSendEmail) {
				  $scope.validateSendEmail();
			  }
		  }, true); // Deep watch to detect changes in object properties

		  // Watch for changes in SendEmailList to validate email selection
		  $selfScope.$watch('ASC.SendEmailList', function() {
			  // Validate email selection whenever email addresses change
			  if ($scope.validateSendEmail) {
				  $scope.validateSendEmail();
			  }
		  }, true); // Deep watch to detect changes in object properties
	  }
      function loadRequests(page,status){
		 AssessmentRequestManager.loadRequest(page,status,api,$scope);

      }
      function resetAssessment() {
         // Use the UI Manager to reset the assessment
         UIManager.resetAssessment($scope, atomic.ActiveSY);
         // Apply auto discounts
         assignAutoDiscounts();
      }

      function applyDiscounts(policyData,planId) {
         AssessmentCalculator.applyDiscounts(policyData, planId, $scope.Assessment);
      }

      function assignAutoDiscounts(){
         // Use the DiscountEligibility module to assign auto discounts
         $scope.Assessment = DiscountEligibility.assignAutoDiscounts($scope.Assessment, Discounts);
      }

      function updateAssessmentDetails(yearLevelKey, planId) {
		 $scope.TuitionData = [];
		 $scope.PaySchedData = [];
        let  assessDetails=  AssessmentCalculator.updateAssessmentDetails(yearLevelKey, planId, $scope.Assessment, $SMC, $filter);
		if(assessDetails){
			$scope.TuitionData =  assessDetails.TuitionData;
			$scope.PaySchedData =  assessDetails.PaySchedData;
		}

      }
      function checkAllowedPlans(yearLevelKey){
         $scope.PaymentPlans = AssessmentCalculator.checkAllowedPlans(yearLevelKey);
      }

	   function checkReservation(registrationNo) {
		   let Assessment = $scope.Assessment;
		   ReservationManager.checkReservation(registrationNo, Assessment,  api,updateAssessmentDetails,$scope);

	   }

      $scope.filterRequests = function(){
         let status =  $scope.FilterStatus;
         loadRequests(1,status);
      }
      $selfScope.$watch('ASC.StudentObj',function(stud){
         if(!stud) return;
         if(!stud.id) return resetAssessment();

         // Set student info
         $scope.Assessment.student_id = stud.id;
         // If test mode, set year level to null and show all year levels
         if(stud.test_mode){
            $scope.Assessment.year_level_id = null;
            $scope.Assessment.department_id = null;
            $scope.YearLevels  =  $SMC.YearLevels;
            assignAutoDiscounts();
            return;
         }
         // If not test mode, set year level and department
         $scope.Assessment.year_level_id = stud.year_level_id;
         $scope.Assessment.department_id = stud.department_id;
         $scope.Assessment.enroll_status = stud.enroll_status;
         let studStatus = stud.enroll_status;

         // Find the index of student's year level in YearLevels array
         let yearLevelIndex = $SMC.YearLevels.findIndex(function(yl) {
             return yl.id === stud.year_level_id;
         });

         // Filter year levels based on department and exclude CLB
         $scope.YearLevels = $filter('filter')($SMC.YearLevels, function(yl) {
             // For old students, include current and next year level
             if (studStatus === 'OLD') {
                 return yl.id === stud.year_level_id ||
                        yl.id === $SMC.YearLevels[yearLevelIndex + 1]?.id;
             }
             // For new students, only include current year level
             return yl.id === stud.year_level_id;
         });
         if(studStatus === 'OLD')
            $scope.Assessment.year_level_id = $scope.YearLevels[1].id;

         assignAutoDiscounts();
		 checkReservation(stud.id);
      });

      $selfScope.$watch('ASC.Assessment.year_level_id', function(yearLevel) {

         if (!yearLevel){
            $scope.TuitionData = [];
            $scope.PaySchedData = [];
            applyDiscounts(null,'PLANA');
            return;
         }

         // Only set default payment plan to CASH when year level changes if no plan is already selected
         // This prevents overriding the payment plan from the assessment request
         let currentPlan = $scope.Assessment.payment_plan_id;
         if (!currentPlan || currentPlan === '') {
            $scope.Assessment.payment_plan_id = 'PLANA';
            currentPlan = 'PLANA';
         }

         // Update assessment details with new year level
         let yearLevelKey = yearLevel;
         let yearLevelObj = $filter('filter')($SMC.YearLevels, {id: yearLevelKey})[0];
         if (yearLevelObj) {
            updateAssessmentDetails(yearLevelKey, currentPlan);
         }
         checkAllowedPlans(yearLevelKey);
      });

      // Watch for changes in textbooks_supplies_option
      // When switching from COMPLETE to SELECTED, initialize custom amounts with default values
      $selfScope.$watch('ASC.Assessment.textbooks_supplies_option', function(newOption, oldOption) {
         if (newOption ===  'COMPLETE' && $scope.Assessment.summary_details) {
            // Initialize custom amounts with the current default values
            $scope.Assessment.custom_textbook_amount = $scope.Assessment.summary_details.textbook_amount;
            $scope.Assessment.custom_supplies_amount = $scope.Assessment.summary_details.supplies_amount;
         }
      });

      $selfScope.$watch('ASC.Assessment.payment_plan_id',function(planId){
         if(!planId) return;
         updateAssessmentDetails($scope.Assessment.year_level_id, planId);
         assignAutoDiscounts();
      });

		$scope.toggleAppMode = function(){
         UIManager.toggleAppMode($scope);
      }
      $scope.cancelAssessment = function(){
         UIManager.cancelAssessment($scope, resetAssessment);
      }
      $scope.payAssessment = function(){
            let data = {amount:$scope.Assessment.assessment_total,assessment_no:'CJA-TEST-123'};
            let success = function(response){
               console.log(response);
               window.open(response.redirectUrl);
            };
            let error = function(response){};
            api.POST('students/maya_link',data,success,error);
      }

      $scope.clearAssessment = function(){
            // Use the UI Manager to clear the assessment
            loadRequests(1,'PENDING');
            UIManager.clearAssessment(aModal);
      }

      $scope.confirmAssessment = function(){
            $scope.AssessmentID  = null;
            $scope.isConfirming  = true;
            $scope.isConfirmed = false;
            $scope.RegObj = null;
            $scope.AppDetails = null;
            $scope.isEmailSent = false;
            // Prepare data for saving
            let assessmentData = {
                student_id: $scope.Assessment.student_id,
                registration_no: $scope.Assessment.registration_no,
                request_no: $scope.Assessment.request_no,
                year_level_id: $scope.Assessment.year_level_id,
                payment_plan_id: $scope.Assessment.payment_plan_id,
                assessment_total: $scope.Assessment.assessment_total,
                summary_details: $scope.Assessment.summary_details
            };

            // Exclude student_id if it starts with CJR
            if(assessmentData.student_id.startsWith('CJR')){
               assessmentData.student_id = null;
            }
            // You can add API call here to save the assessment
            console.log('Assessment confirmed:', assessmentData);

            // Close the modal
            aModal.close('StudentAssessmentResponsetModal');

            // Show success message
            let success =function(response){
               console.log(response);
               $scope.isConfirming  = false;
               $scope.isConfirmed = true;
               $scope.AssessmentID = response.data.id;
               let regNo =  response.data.registration_no;
               requestRegistration(regNo);
               loadRequests(1,'PENDING');
            };
            let error =function(response){
               $scope.isConfirming  = false;
            };

            api.POST('assessments',assessmentData,success,error);

            function requestRegistration(regNo){
               let success = function(response){
                  $scope.RegObj = response.data[0];
                  $scope.AppDetails = JSON.parse($scope.RegObj.application_details);
                  $scope.EmailOptions = [];
                  $scope.SendEmailFlags = {};
                  $scope.SendEmailList = {};
                  $scope.isEmailValid = false; // Initialize email validation state
                  if($scope.AppDetails.guardian){
                     let guardian = $scope.AppDetails.guardian;
                     let emailObj,email;
                     if(guardian.father_email){
                        emailObj = {key:'FATHER',rel:'Father\'s Email', is_selected: guardian.notify_father=='Y', email:guardian.father_email};
                        $scope.EmailOptions.push(emailObj);
                     }

                     if(guardian.mother_email){
                        emailObj = {key:'MOTHER',rel:'Mother\'s Email', is_selected: guardian.notify_mother=='Y',email:guardian.mother_email};
                        $scope.EmailOptions.push(emailObj);
                     }

                     if(guardian.guardian_email){
                        emailObj = {key:'GUARDIAN',rel:'Guardian\'s Email', is_selected: guardian.notify_guardian=='Y',email:guardian.guardian_email};
                        $scope.EmailOptions.push(emailObj);
                     }


                     const isLocal =  location.host =='localhost';
                     const testEmail = '<EMAIL>';
                     $scope.EmailOptions.map((eObj)=>{
                        $scope.SendEmailFlags[eObj.key] = eObj.is_selected;
                        $scope.SendEmailList[eObj.key] = isLocal?testEmail:eObj.email;
                     });

                     // Validate email selection after initialization
                     if ($scope.validateSendEmail) {
                        $scope.validateSendEmail();
                     }
                  }
               };
               let error = function(response){};
               api.GET('inquiries',{id:regNo},success,error);
            }
      }
      $scope.previewConfirmation = function(){
         // Prepare data for email preview
         let assessmentId = $scope.AssessmentID;
         let emails = [];

         // Collect selected emails
         for (let key in $scope.SendEmailFlags) {
            if ($scope.SendEmailFlags[key] && $scope.SendEmailList[key]) {
               emails.push($scope.SendEmailList[key]);
            }
         }

         // Update email validation state
         $scope.validateSendEmail();

         // Create URL with parameters
         let previewUrl = '../api/assessments/preview_email?id=' + assessmentId;

         // Add emails as parameters if available
         if (emails.length > 0) {
            previewUrl += '&emails=' + encodeURIComponent(emails.join(','));
         }
         console.log(previewUrl);
         // Open in a new window
         window.open(previewUrl, '_blank', 'width=800,height=600');
      }

      $scope.previewAttachment = function(){
         // Prepare data for attachment preview
         let assessmentId = $scope.AssessmentID;

         if (!assessmentId) {
            alert('Assessment ID is required to preview the attachment');
            return;
         }

         // Create URL for the attachment preview
         let previewUrl = '../api/assessments/preview_attachment?id=' + assessmentId;

         // Open in a new window
         window.open(previewUrl, '_blank', 'width=800,height=600');
      }

	  $scope.saveAssessment = function(){
		  // Use the DiscountEligibility module to validate discount cap
		  if (DiscountEligibility.validateDiscountCap($scope.Assessment)) {
			  // Alert the user and prevent saving
			  alert('Cannot proceed with assessment. Total discounts (₱' +
				($scope.Assessment.total_discount || 0) + ') exceed the gross tuition fee (₱' +
				$scope.Assessment.gross_tuition_fee + ') by ₱' +
				$scope.Assessment.discount_excess_amount + '. Please adjust the discounts.');
			  return;
		  }
        // Summarize details
		  AssessmentCalculator.summarizeDetails( $scope.Assessment, $scope.PaySchedData);
        // Get year level and payment plan
        let yearLevelKey = $scope.Assessment.year_level_id;
        let planId = $scope.Assessment.payment_plan_id;
		  // Store student information
		  $scope.Assessment.summary_details.student_name = $scope.StudentObj.name;
		  $scope.Assessment.summary_details.student_type = $scope.Assessment.enroll_status;
		  $scope.Assessment.summary_details.year_level = ($filter('filter')($SMC.YearLevels, {id: yearLevelKey})[0] || {}).description;
		  $scope.Assessment.summary_details.payment_plan = ($filter('filter')($scope.PaymentPlans, {id: planId})[0] || {}).name;

		  // Open the modal
        $scope.isConfirmed = false;
        $scope.AssessmentID = null;
		  aModal.open('StudentAssessmentConfirmationModal');
	  }

      $scope.closeAssessment = function(){
         UIManager.closeAssessment(aModal);
      }
      $scope.doneAssessment = function(){
         $scope.StudentObj = {id: null, name: null};
         resetAssessment();
         loadRequests(1,'PENDING');
         UIManager.closeAssessment(aModal);
      }
      // Validate if at least one email is selected
      $scope.validateSendEmail = function() {
         $scope.isEmailValid = false;

         // Check if at least one email is selected
         for (let key in $scope.SendEmailFlags) {
            if ($scope.SendEmailFlags[key] && $scope.SendEmailList[key]) {
               $scope.isEmailValid = true;
               break;
            }
         }

         return $scope.isEmailValid;
      };

      $scope.sendConfirmation = function(){
         $scope.isEmailSent = false;
         // Validate emails before sending
         if (!$scope.validateSendEmail()) {
            alert('Please select at least one email recipient before sending.');
            return;
         }

         $scope.isSending = true;

         // Prepare data for sending email
         let assessmentId = $scope.AssessmentID;
         let emailData = {
            assessment_id: assessmentId,
            emails: []
         };

         // Collect selected emails
         for (let key in $scope.SendEmailFlags) {
            if ($scope.SendEmailFlags[key] && $scope.SendEmailList[key]) {
               emailData.emails.push($scope.SendEmailList[key]);
            }
         }

         // Call API to send email
         let success = function(response){
            $scope.isSending = false;
            alert('Email with assessment PDF attachment sent successfully!');
            $scope.isEmailSent = true;
         };

         let error = function(response){
            $scope.isSending = false;
            alert('Failed to send email. Please try again.');
         };

         api.POST('assessments/send_email', emailData, success, error);
      }

      $scope.viewRequests = function(){
		   $scope.clearActiveRequest();
         UIManager.viewRequests($scope, aModal);
      }

      $scope.gotoPage = function(page){
         let status =  $scope.FilterStatus;
         loadRequests(page,status);
      }

      $scope.processAssessment = function(AObj){
         let filter ={keyword:AObj.student_id, fields:['id']};
         if(!AObj.student_id)
            filter.keyword = AObj.ref_no;
         let success =function(response){
           let  student = response.data[0];
            $scope.StudentObj = {id:filter.keyword, name:student.display_name,year_level_id:student.year_level_id,enroll_status:student.enroll_status};

            $scope.Assessment.year_level_id = AObj.year_level_id;
            let yearLevelObj = $filter('filter')($SMC.YearLevels,{id: AObj.year_level_id})[0];
            $scope.Assessment.department_id =  yearLevelObj.department_id;
            $scope.Assessment.request_no =  AObj.id;
            $scope.Assessment.registration_no = AObj.ref_no;

            // Set the payment plan from the assessment request
            if (AObj.payment_plan_id) {
               $scope.Assessment.payment_plan_id = AObj.payment_plan_id;
            }

         // Modified switch statement to handle undefined/null textbook_option
         switch(AObj.textbook_option){
            case 'COMPLETE': case 'SELECTED':
               $scope.Assessment.purchase_textbooks_supplies = 'Y';
               $scope.Assessment.textbooks_supplies_option = AObj.textbook_option;
               break;
            case 'NONE':
               $scope.Assessment.purchase_textbooks_supplies = 'N';
               $scope.Assessment.textbooks_supplies_option = null;
               break;
            default: // Handle case when textbook_option is not selected
               $scope.Assessment.purchase_textbooks_supplies = 'N';
               $scope.Assessment.textbooks_supplies_option = null;
               break;
         }

            // Check and apply reservation fee if applicable
            if (AObj.ref_no) {
               checkReservation(AObj.ref_no);
            }

            aModal.close('StudentAssessmentRequestModal');
         };
         let error =function(response){};
         api.GET('students/search',filter,success,error);
      }
	  $scope.clearActiveRequest = function(){
		  $scope.ActiveRequest =null;
		  $scope.RegNos = null;
		  $scope.RegResults = null;
	  }
	  $scope.deleteRequest = function(){
		  let reqId = $scope.ActiveRequest.id;
		  let record = {id:reqId, status:'ARCHIVE'};
		  let success = function(response){
			  loadRequests();
		  };
		  let error = function(response){};
		  if(confirm(`Are you sure you want to archive ${reqId}?`)){
			return api.PUT('assessment_requests',record,success,error);
		  }
	  }
	  $scope.searchRegistration = function(){
			$scope.isSearching = true;
			$scope.ActiveRequest.ref_no = null;
			$scope.RegNos = null;
			$scope.RegResults = null;
			let searchName = $scope.ActiveRequest.student_name;
			ReservationManager.searchStudent(searchName, api, $scope,$timeout);
	  }
    }]);
});
