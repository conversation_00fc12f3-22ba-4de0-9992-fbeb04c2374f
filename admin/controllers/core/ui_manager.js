"use strict";

define(['app'], function(app) {
    var module = {};

    /**
     * Prepares the UI elements for the assessment controller
     * @param {Object} $scope - The controller scope
     * @param {Object} Discounts - The discounts configuration object
     * @param {Array} PAYMENT_PLANS - The payment plans array
     * @param {Object} $SMC - The shared model controller
     * @param {Object} DiscountEligibility - The discount eligibility module
     * @returns {Object} UI configuration object
     */
    module.prepareUI = function($scope, Discounts, PaymentPlans, $SMC, DiscountEligibility) {
        // Prepare Discount Text using the DiscountEligibility module
        $scope.DiscountText = DiscountEligibility.prepareDiscountText(Discounts);

        // Prepare Payment Plans
        $scope.PaymentPlans = PaymentPlans;

        // Prepare Lookup Lists
        $scope.__YearLevels = {};
        $SMC.YearLevels.map(yrLevel => {
            $scope.__YearLevels[yrLevel.id] = yrLevel;
        });

        $scope.__PaymentPlans = {};
        $scope.PaymentPlans.map(pPlan => {
            $scope.__PaymentPlans[pPlan.id] = pPlan;
        });

        // Prepare Discount Visibility
        $scope.showReservationFee = true; // Variable to control visibility of Reservation Fee panel
        $scope.showWaivedRegistrationFee = true; // Variable to control visibility of Waived Registration Fee panel
        $scope.showEarlyBird = true; // Variable to control visibility of Early Bird panel
        $scope.showSiblingDiscount = false; // Variable to control visibility of Sibling Discount panel
        $scope.showEmployeeDiscount = false; // Variable to control visibility of Employee Discount panel
        $scope.showEscVoucher = false; // Variable to control visibility of ESC/Voucher panel
        $scope.showSportScholarship = false; // Variable to control visibility of Sport Scholarship panel
        $scope.showAcademicScholarship = false; // Variable to control visibility of Academic Scholarship panel
        $scope.showTextbookSupplies = true; // Variable to control visibility of Textbook and Supplies panel

    };

    /**
     * Sets up the right panel for fixed positioning when on the Adjustments tab
     * @param {Object} $selfScope - The parent scope
     * @param {Object} $scope - The controller scope
     * @param {Function} $timeout - Angular's $timeout service
     */
    module.setupRightPanel = function($selfScope, $scope, $timeout) {
        $timeout(function computeRightPanel() {
            let rightPanel = document.querySelector('.right-panel>div.row');
            let rightPanelUIs = document.querySelectorAll('.right-panel>*');
            if (!rightPanel) return $timeout(computeRightPanel, 2000);

            let rightPanelWidth = rightPanel.offsetWidth - 50;
            console.log(rightPanelWidth, rightPanel);

            $selfScope.$watch('ASC.ActiveTabIndex', function(tabIndex) {
                // When Adjustment tab is selected, make the right panel fixed
                if (tabIndex == 2) {
                    rightPanelUIs.forEach((ui, index) => {
                        let width = rightPanelWidth;
                        if (index == 0)
                            width = width + 30;
                        ui.style.position = 'fixed';
                        ui.style.width = width + 'px';
                    });
                } else {
                    rightPanelUIs.forEach(ui => {
                        ui.style.position = 'relative';
                        delete ui.style.width;
                    });
                }
            });
        }, 1000);
    };

    /**
     * Initializes the UI components for the assessment controller
     * @param {Array} SchoolYears - The school years array
	 * @param {String} activeSY - The active school year
	 * @param {Object} $scope - The controller scope
     * @param {Object} $filter - Angular's $filter service
     */
    module.initUI = function(SchoolYears , activeSY, $scope, $filter) {
        $scope.SchoolYears = $filter('filter')(SchoolYears, {id: activeSY});

        $scope.StuObjFields = ['id', 'lrn', 'sno', 'year_level_id', 'display_name', 'enroll_status', 'prev_school_type'];

        $scope.TuitionHeaders = ['Fee', {label: 'Amount', class: 'numeric col-md-6 text-right'}];
        $scope.TuitionProps = ['fee', 'display_amount'];
        $scope.TuitionData = [];

        $scope.PaySchedHeaders = ['Schedule', {label: 'Amount', class: 'col-md-6 numeric text-right'}];
        $scope.PaySchedProps = ['schedule', 'display_amount'];
        $scope.PaySchedData = [];

        // Set AppMode to TEST by default when running on localhost
        $scope.AppMode = window.location.hostname === 'localhost' ? 'TEST' : 'NORMAL';

        $scope.RequestsCounter = 0;

        $scope.RequestsHeaders = [
                        {label:'Request No.', class:'col-md-2'},
                        'Student',
                        {label:'Incoming Level', class:'col-md-2'},
                        {label:'Payment Plan.', class:'col-md-2'}];
        $scope.RequestsProps = ['id', 'student_name', 'year_level', 'payment_plan'];
        $scope.RequestsData = [];
        $scope.FilterStatus = 'PENDING';
        $scope.FilterStatusOptions = [
                {id:'PENDING', name:'Pending'},
                {id:'PROCESSED', name:'Processed'},
                {id:'ARCHIVE', name:'Archived'},
                {id:'ALL', name:'All'}
            ];
    };

    /**
     * Resets the assessment UI to its default state
     * @param {Object} $scope - The controller scope
     * @param {String} activeSY - The active school year
     */
    module.resetAssessment = function($scope, activeSY) {
        $scope.Assessment = {
            has_child_discount: 'N',
            has_scholarship: 'N',
            has_academic_scholarship: 'N', // Default to no academic scholarship
            academic_scholarship_discount: 0, // Default discount amount
            esp: activeSY,
            payment_plan_id: 'PLANA', // default to cash basis
            track: 'STEM', // Default track for senior high school students
            assessment_total: 0,
            has_reservation_fee: 'N', // Default to no reservation fee
            reservation_fee_amount: 4500, // Default reservation fee amount,
            has_waived_registration_fee: 'N', // Default to no waived registration fee
            purchase_textbooks_supplies: 'Y', // Default to Yes for purchasing textbooks and supplies
            textbooks_supplies_option: 'COMPLETE', // Default to Complete set
            custom_textbook_amount: 0, // Custom amount for textbooks when 'Selected items' is chosen
            custom_supplies_amount: 0, // Custom amount for school supplies when 'Selected items' is chosen
        };

        $scope.TuitionData = [];
        $scope.PaySchedData = [];
        $scope.ActiveTabIndex = 0;

        if ($scope.AppMode == 'TEST')
            $scope.TestModeStudentType = null;
    };

    /**
     * Toggles the application mode between NORMAL and TEST
     * @param {Object} $scope - The controller scope
     */
    module.toggleAppMode = function($scope) {
        $scope.AppMode = $scope.AppMode == 'NORMAL' ? 'TEST' : 'NORMAL';
    };

    /**
     * Cancels the current assessment after confirmation
     * @param {Object} $scope - The controller scope
     * @param {Function} resetAssessment - The function to reset the assessment
     */
    module.cancelAssessment = function($scope, resetAssessment) {
        if (confirm('Are you sure you want to reset the assessment?')) {
            $scope.StudentObj = {id: null, name: null};
            resetAssessment();
        }
    };

    /**
     * Updates the discount visibility based on student year level
     * @param {Object} $scope - The controller scope
     * @param {String} yearLevel - The student's year level
     * @param {Object} DiscountEligibility - The discount eligibility module
     */
    module.updateDiscountVisibility = function($scope, yearLevel, DiscountEligibility) {
        // Use the DiscountEligibility module to update discount visibility
        DiscountEligibility.updateDiscountVisibility($scope, yearLevel);
    };

    /**
     * Sets up the watch for the test mode student type
     * @param {Object} $selfScope - The parent scope
     * @param {Object} $scope - The controller scope
     */
    module.setupTestModeWatch = function($selfScope, $scope) {
        $selfScope.$watch('ASC.TestModeStudentType', function(studentType) {
            if (!studentType) return;
            if (studentType === 'NEW') {
                $scope.StudentObj = { id: 'TESTNEW', name: 'New Student', enroll_status: 'NEW', test_mode: true };
            } else if (studentType === 'OLD') {
                $scope.StudentObj = { id: 'TESTOLD', name: 'Old Student', enroll_status: 'OLD', test_mode: true };
            }
        });
    };

    /**
     * Sets up the watch for disabling view requests button
     * @param {Object} $selfScope - The parent scope
     * @param {Object} $scope - The controller scope
     */
    module.setupDisableViewRequestsWatch = function($selfScope, $scope) {
        $selfScope.$watchGroup(['ASC.StudentObj.id', 'ASC.AppMode', 'ASC.RequestsCounter'], function(vals) {
            $scope.disableViewRequests = vals[0] || vals[1] == 'TEST' || vals[2] == 0;
        });
    };

    /**
     * Opens the assessment request modal
     * @param {Object} $scope - The controller scope
     * @param {Object} aModal - The modal service
     */
    module.viewRequests = function($scope, aModal) {
        $scope.clearActiveRequest();
        aModal.open('StudentAssessmentRequestModal');
    };

    /**
     * Closes the assessment confirmation modal
     * @param {Object} aModal - The modal service
     */
    module.closeAssessment = function(aModal) {
        aModal.close('StudentAssessmentConfirmationModal');
    };

    /**
     * Clears the assessment modal
     * @param {Object} aModal - The modal service
     */
    module.clearAssessment = function(aModal) {
        aModal.close('StudentAssessmentResponsetModal');
    };

    return module;
});
