"use strict";

define([], function() {
    var module = {};

    /**
     * Checks and applies reservation fee based on registration number.
     *
     * @param {string} registrationNo The registration number to check.
     * @param {object} Assessment The Assessment object.
     * @param {object} api The API service.
     * @param {function} updateAssessmentDetails Function to update assessment details.
     */
    module.checkReservation = function(registrationNo, Assessment, api, updateAssessmentDetails,$scope) {
        if (!registrationNo) {
            return;
        }

        if (registrationNo.startsWith('CJR')) {
            // Fetch registration information based on registration number
            // Include application_details to extract student source information
            let regFilter = {keyword: registrationNo, fields: ['id', 'application_details']};
            let regSuccess = function(regResponse) {
                if (regResponse.data && regResponse.data.length > 0) {
                    let registration = regResponse.data[0];
                    // Set the registration number
                    Assessment.registration_no = registration.id;
                   // Update student source based on registration information
                   module.updateSchoolSource(registration, Assessment);
                    // Apply reservation fee if status is approved or validated
                    module.applyReservationFee(registration, Assessment, updateAssessmentDetails,$scope);
                }
            };
            let regError = function(response) {
                console.error('Error fetching registration details:', response);
            };

            // Fetch registration details
            api.GET('inquiries', regFilter, regSuccess, regError);
        } else {
            alert(`Invalid Registration No. ${registrationNo}`);
        }
    };

    /**
     * Applies reservation fee based on registration status.
     *
     * @param {object} registration The registration object.
     * @param {object} Assessment The Assessment object.
     * @param {function} updateAssessmentDetails Function to update assessment details.
     */
    module.applyReservationFee = function(registration, Assessment, updateAssessmentDetails,$scope) {
        $scope.ReservationInvalid = false;
        if (registration.status === 'APPROVED' || registration.status === 'VALIDATED') {
            // Get the paid amount from the registration record
            let paidAmount = registration.paid_amount || 4500; // Default to 4500 if not specified

            // Set reservation fee to Yes and update the amount
            Assessment.has_reservation_fee = 'Y';
            Assessment.reservation_fee_amount = parseFloat(paidAmount);
           

            // Update assessment if year level and payment plan are selected
            if (Assessment.year_level_id && Assessment.payment_plan_id) {
                updateAssessmentDetails(Assessment.year_level_id,Assessment.payment_plan_id);
            }
        } else {
            // Show notification to the user
            Assessment.has_reservation_fee = 'Y';
            Assessment.reservation_fee_amount = 0;
            alert(`Registration No.${registration.id} found. Not yet approved or validated.`);
            $scope.ActiveTabIndex = 2;
            $scope.ReservationInvalid = true;
        }
    };
    /**
     * Updates the student source in the Assessment object based on registration data.
     * 
     * @param {object} registration The registration object containing application details.
     * @param {object} Assessment The Assessment object to update with student source.
     */
    module.updateSchoolSource = function(registration, Assessment){
        // Auto-populate student source from application details if available
        let schoolSource = 'PRIVATE';
        if (registration.application_details) {
          
            try {
                let appDetails = typeof registration.application_details === 'string'
                    ? JSON.parse(registration.application_details)
                    : registration.application_details;
                  console.log(appDetails);
                if (appDetails.scholastic && appDetails.scholastic.school_type) {
                    // Map school_type to student_source for academic scholarships
                   schoolSource = appDetails.scholastic.school_type === 'PUB' ? 'PUBLIC' : 'PRIVATE';
                }
            } catch (error) {
                console.warn('Error parsing application details:', error);
            }
        }

        Assessment.student_source = schoolSource;
    };

    /**
     * Sets up watchers for reservation fee related fields.
     *
     * @param {object} $selfScope The parent scope.
     * @param {object} $scope The controller scope.
     * @param {function} updateAssessmentDetails Function to update assessment details.
     */
    module.setupReservationWatchers = function($selfScope, $scope, updateAssessmentDetails) {
        // Watch for changes in reservation fee fields
        $selfScope.$watchGroup([
            'ASC.Assessment.has_reservation_fee',
            'ASC.Assessment.reservation_fee_amount'
        ], function() {
            if (!$scope.Assessment) return;

            // Only recompute if we have a year level and payment plan selected
            if ($scope.Assessment.year_level_id && $scope.Assessment.payment_plan_id) {
                updateAssessmentDetails($scope.Assessment.year_level_id, $scope.Assessment.payment_plan_id);
            }
        });
    };

    /**
     * Searches for a student by name.
     *
     * @param {string} searchName The name to search for. Accepts comma separated values.
     * @param {object} api The API service.
     * @param {object} $scope The controller scope.
     * @param {object} $timeout The $timeout service.
     */
    module.searchStudent = function(searchName, api, $scope,$timeout){

			let keywords = searchName.split(',');
			let searchResults = {};
			let searchHits = {};


			function performSearch(keywords,index){
					let keyword = keywords[index].trim();
					if(keyword.length==1)
						return checkSearchResults();
					$scope.searchWord = keyword;
					let filter = {fields:['first_name','last_name','middle_name'],keyword:keyword};
					let success =  function(response){
						let data = response.data;
						data.map(result=>{
							let regId =  result.id;
							if(regId.startsWith("CJR")){
								if(!searchResults[regId]){
									result.name = regId;
									searchResults[regId] =result;
									searchHits[regId] = 0;
								}
								searchHits[regId]++;
							}
						});
					};
					let error =  function(response){};

					$timeout(function(){
						api.GET('inquiries',filter, success,error).then(()=>{
							if(index < keywords.length-1){
								return performSearch(keywords, index+1);
							}else{
								checkSearchResults();
							}
						});
					},500);

			}

			function checkSearchResults(){

				$timeout(function(){
					$scope.isSearching = false;
					$scope.searchWord =null;

					// Step 1: Find the max number of hits
					let maxHits = Math.max(...Object.values(searchHits));

					// Step 2: Filter results to only include those with max hits
					let filteredRegIds = Object.keys(searchHits).filter(regId => searchHits[regId] === maxHits);

					// Step 3: If more than one top result, show choices. Else, auto-select.
					if(filteredRegIds.length > 1){
						$scope.RegNos = filteredRegIds.map(id => searchResults[id]);
						$scope.RegResults = searchResults;
					} else {
						$scope.ActiveRequest.ref_no = filteredRegIds[0];
					}
				},1000);
			}


			performSearch(keywords,0);


	}

	return module;
});
