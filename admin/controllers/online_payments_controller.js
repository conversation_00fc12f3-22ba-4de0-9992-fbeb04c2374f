"use strict";
define(['app','atomic/bomb','api'], function (app) {
    app.register.controller('OnlinePaymentsController',['$scope','api','$filter','Atomic','aModal',function ($scope,api,$filter,atomic,aModal) {
        const $selfScope = $scope;
        $scope = this;
        $scope.init = function () {
            // Initialize the controller
            $scope.payments = [];
            $scope.headers = ['Date Submitted','Ref No.','Student Name','Grade Level','Payment Type','Amount Paid','Status'];
            $scope.props = ['transaction_date','ref_no','student_name','year_level','payment_method_display','amount_display','payment_status_display'];
            $scope.isLoading = false;
            $scope.CurrentPage = 1;
            $scope.isRecordsFiltered = false;
            $scope.showSearch = false;

            // Initialize Meta object for pagination
            $scope.Meta = {page: 0, pages: 0, limit: 0, count: 0};

            // Initialize filter values
            $scope.DisplayFilter = 'PENDING'; // Default to pending payments
            $scope.DateFilterFrom = null;
            $scope.DateFilterTo = null;
            $scope.StudentSearch = null;

            $scope.AppStatuses = [
                {id:'PENDING',name:'Pending'},
                {id:'APPROVED',name:'Approved'},
                {id:'VALIDATED',name:'Validated'},
                {id:'DECLINED',name:'Declined'},
                {id:'ALL',name:'All'}
            ];

            $scope.PaymentStatusOptions = [
                {id:'PENDING',name:'Pending'},
                {id:'APPROVED',name:'Approved'},
                {id:'VALIDATED',name:'Validated'},
                {id:'DECLINED',name:'Declined'}
            ];

            $scope.PayModeOptions = [
                {id:'GCASH',name:'GCash'},
                {id:'PMAYA',name:'Pay Maya'},
                {id:'BANKT',name:'Bank Transfer'},
                {id:'ONSIT',name:'On-Site Payment'},
                {id:'NOPAY',name:'Skipped Required Payment'},
                {id:'NONE',name:'No Payment'}
            ];

            $scope.loadPayments();
        };

        $scope.loadPayments = function (page, from, to, search) {
            $scope.isLoading = true;
            $scope.payments = [];

            let filter = {page: page || 1};

            // Apply status filter
            if($scope.DisplayFilter && $scope.DisplayFilter !== 'ALL') {
                filter.payment_status = $scope.DisplayFilter;
            }

            // Apply date range filter
            if(from && to) {
                filter.from = $filter('date')(from, 'yyyy-MM-dd');
                filter.to = $filter('date')(to, 'yyyy-MM-dd');
            }

            // Apply search filter
            if(search) {
                delete filter.from;
                delete filter.to;
                filter.keyword = search.keyword;
                filter.fields = search.fields;
            }

            $scope.CurrentPage = page || 1;

            api.GET('online_payments', filter, $scope.success, $scope.error);
        };

        let success = function(response){
            $scope.isLoading = false;
            let payments = response.data;
            for(var i in payments){
                let payment = payments[i];
                let status = payment.payment_status;
                let transactionDate = payment.transaction_date;

                switch(status){
                    case 'APPROVED':
                        payments[i].class = 'success';
                    break;
                    case 'DECLINED':
                        payments[i].class = 'danger';
                    break;
                    case 'PENDING':
                        payments[i].class = 'warning';
                    break;
                    case 'VALIDATED':
                        payments[i].class = 'info';
                    break;
                }

                // Format transaction date
                payment.transaction_date = $filter('date')(new Date(transactionDate), 'MMM d, yyyy');

                // Format amount as currency
                payment.amount_display = $filter('currency')(payment.amount, '₱');

                // Format payment method for display
                switch(payment.payment_method){
                    case 'GCASH':
                        payment.payment_method_display = 'GCash';
                    break;
                    case 'PMAYA':
                        payment.payment_method_display = 'PayMaya';
                    break;
                    case 'BANKT':
                        payment.payment_method_display = 'Bank Transfer';
                    break;
                    case 'ONSIT':
                        payment.payment_method_display = 'On-site';
                    break;
                }

                // Format payment status for display

                payment.payment_status_display =  payment.payment_status;
                
                // Extract incoming level from assessment details if available
                if(payment.Assessment && payment.year_level){
                    try {

                        payment.year_level = year_level|| 'N/A';
                    } catch(e) {
                        payment.incoming_level = 'N/A';
                    }
                } else {
                    payment.incoming_level = 'N/A';
                }

                payments[i] = payment;
            }
            $scope.payments = payments;
            $scope.Meta = response.meta;
        };

        let error = function(response){
            console.error('Error loading payments:', response);
            $scope.isLoading = false;
            $scope.Meta = {page: 0, pages: 0, limit: 0, count: 0};
            $scope.CurrentPage = 0;
        };

        $scope.success = success;
        $scope.error = error;

        // Add watchers for date filter synchronization
        $selfScope.$watch('OPC.DateFilterFrom', function(dateFrom) {
            $scope.DateFilterTo = dateFrom;
        });

        // Filter methods
        $scope.gotoPage = function(page) {
            let from = $scope.DateFilterFrom;
            let to = $scope.DateFilterTo;
            $scope.loadPayments(page, from, to);
        };

        $scope.filterRecords = function() {
            $scope.isRecordsFiltered = true;
            let from = $scope.DateFilterFrom;
            let to = $scope.DateFilterTo;
            let page = 1;
            $scope.loadPayments(page, from, to);
        };

        $scope.toggleSeach = function() {
            $scope.showSearch = !$scope.showSearch;
        };

        $scope.searchRecords = function() {
            $scope.isRecordsFiltered = true;
            let keyword = $scope.StudentSearch;
            let fields = ['ref_no', 'student_name', 'payment_reference'];
            let search = {keyword: keyword, fields: fields};
            let page = 1;
            $scope.loadPayments(page, null, null, search);
        };

        $scope.clearFilter = function() {
            $scope.isRecordsFiltered = false;
            $scope.DateFilterTo = null;
            $scope.DateFilterFrom = null;
            $scope.StudentSearch = null;
            $scope.loadPayments(1);
        };

        $scope.init();

       $scope.openModal = function(item){
        aModal.open('ActivePaymentModal');
            $scope.ActivePayment = item;
            $scope.AttachmentURL = null;
            if(item.attachment)
                $scope.AttachmentURL = '../api/attachments/view/'+item.attachment.file_name;
            $scope.ActivePayment.confirmation_date = angular.copy(item.confirmation_date? new Date(item.confirmation_date): new Date());
            console.log($scope.ActivePayment);

       }
       $scope.closeModal = function() {
            $scope.ActivePayment = null;
             $scope.AttachmentURL = null;
            aModal.close('ActivePaymentModal');
       }

       $scope.savePayment = function() {
            if (!$scope.ActivePayment || !$scope.ActivePayment.id) {
                console.error('No active payment to save');
                return;
            }

            // Disable the save button to prevent multiple submissions
            $scope.isSaving = true;

            // Prepare the payment data for API
            let paymentData = {
                    id: $scope.ActivePayment.id,
                    amount: $scope.ActivePayment.amount,
                    payment_reference: $scope.ActivePayment.payment_reference,
                    payment_method: $scope.ActivePayment.payment_method,
                    notes: $scope.ActivePayment.notes,
                    payment_status: $scope.ActivePayment.payment_status
            };

            // Format the confirmation date if it's a Date object
            if ($scope.ActivePayment.confirmation_date instanceof Date) {
                paymentData.confirmation_date = $filter('date')($scope.ActivePayment.confirmation_date, 'yyyy-MM-dd HH:mm:ss');
            } else if ($scope.ActivePayment.confirmation_date) {
                paymentData.confirmation_date = $scope.ActivePayment.confirmation_date;
            }

            // Call the API to update the payment
            api.PUT('online_payments', paymentData,
                function(response) {
                    // Success callback
                    $scope.isSaving = false;
                    console.log('Payment saved successfully:', response);

                    // Show success message
                    alert('Payment updated successfully!');

                    // Close the modal
                    $scope.closeModal();

                    // Reload the payments list to reflect changes
                    $scope.loadPayments();
                },
                function(error) {
                    // Error callback
                    $scope.isSaving = false;
                    console.error('Error saving payment:', error);

                    // Show error message
                    let errorMessage = error.message || 'Failed to save payment. Please try again.';
                    alert(errorMessage);
                }
            );
       }
    }]);
});
