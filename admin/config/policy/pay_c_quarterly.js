// admin/config/policy/pay_c_quarterly.js
// Quarterly Payment Structure:
// - First Payment (Upon Enrollment): One-fourth of tuition fee + all miscellaneous fees + all other fees
// - Subsequent Payments (Jul 25, Oct 25, Jan 26): One-fourth of tuition fee each
// Example for Grade 9:
// - First Payment: 8,215 + 5,600 + 12,335 = 26,150
// - Subsequent Payments: 8,215 each
define([], function(){
	var PayCQuarterly = {
		"PLAN_C_QUARTERLY": {
		  "JN": {
			"tuition_fee": 9318,
			"miscellaneous_fees": 4150,
			"other_fees": 10885,
			"total_due": 24353,
			"gross_tuition_fee": 33279,
			"due_dates": ["Upon Enrollment", "July 25, 2025", "Oct 25, 2025", "Jan 26, 2026"],
			"due_amounts": [24353, 9318, 9318, 9318]
		  },
		  "SN": {
			"tuition_fee": 9007,
			"miscellaneous_fees": 4150,
			"other_fees": 10885,
			"total_due": 24042,
			"gross_tuition_fee": 32167,
			"due_dates": ["Upon Enrollment", "July 25, 2025", "Oct 25, 2025", "Jan 26, 2026"],
			"due_amounts": [24042, 9007, 9007, 9007]
		  },
		  "KN": {
			"tuition_fee": 9007,
			"miscellaneous_fees": 4150,
			"other_fees": 13285,
			"total_due": 26442,
			"gross_tuition_fee": 32167,
			"due_dates": ["Upon Enrollment", "July 25, 2025", "Oct 25, 2025", "Jan 26, 2026"],
			"due_amounts": [26442, 9007, 9007, 9007]
		  },
		  "G1": {
			"tuition_fee": 8942,
			"miscellaneous_fees": 4750,
			"other_fees": 11535,
			"total_due": 25227,
			"gross_tuition_fee": 31936,
			"due_dates": ["Upon Enrollment", "July 25, 2025", "Oct 25, 2025", "Jan 26, 2026"],
			"due_amounts": [25227, 8942, 8942, 8942]
		  },
		  "G2": {
			"tuition_fee": 8173,
			"miscellaneous_fees": 4750,
			"other_fees": 11735,
			"total_due": 24658,
			"gross_tuition_fee": 29189,
			"due_dates": ["Upon Enrollment", "July 25, 2025", "Oct 25, 2025", "Jan 26, 2026"],
			"due_amounts": [24658, 8173, 8173, 8173]
		  },
		  "G3": {
			"tuition_fee": 8173,
			"miscellaneous_fees": 4750,
			"other_fees": 11735,
			"total_due": 24658,
			"gross_tuition_fee": 29189,
			"due_dates": ["Upon Enrollment", "July 25, 2025", "Oct 25, 2025", "Jan 26, 2026"],
			"due_amounts": [24658, 8173, 8173, 8173]
		  },
		  "G4": {
			"tuition_fee": 8173,
			"miscellaneous_fees": 4750,
			"other_fees": 12335,
			"total_due": 25258,
			"gross_tuition_fee": 29189,
			"due_dates": ["Upon Enrollment", "July 25, 2025", "Oct 25, 2025", "Jan 26, 2026"],
			"due_amounts": [25258, 8173, 8173, 8173]
		  },
		  "G5": {
			"tuition_fee": 8173,
			"miscellaneous_fees": 5150,
			"other_fees": 12335,
			"total_due": 25658,
			"gross_tuition_fee": 29189,
			"due_dates": ["Upon Enrollment", "July 25, 2025", "Oct 25, 2025", "Jan 26, 2026"],
			"due_amounts": [25658, 8173, 8173, 8173]
		  },
		  "G6": {
			"tuition_fee": 8173,
			"miscellaneous_fees": 5150,
			"other_fees": 14035,
			"total_due": 27358,
			"gross_tuition_fee": 29189,
			"due_dates": ["Upon Enrollment", "July 25, 2025", "Oct 25, 2025", "Jan 26, 2026"],
			"due_amounts": [27358, 8173, 8173, 8173]
		  },
		  "G7": {
			"tuition_fee": 8215,
			"miscellaneous_fees": 5150,
			"other_fees": 12335,
			"total_due": 25700,
			"gross_tuition_fee": 29340,
			"due_dates": ["Upon Enrollment", "July 25, 2025", "Oct 25, 2025", "Jan 26, 2026"],
			"due_amounts": [25700, 8215, 8215, 8215]
		  },
		  "G8": {
			"tuition_fee": 8215,
			"miscellaneous_fees": 5150,
			"other_fees": 12335,
			"total_due": 25700,
			"gross_tuition_fee": 29340,
			"due_dates": ["Upon Enrollment", "July 25, 2025", "Oct 25, 2025", "Jan 26, 2026"],
			"due_amounts": [25700, 8215, 8215, 8215]
		  },
		  "G9": {
			"tuition_fee": 8215,
			"miscellaneous_fees": 5600,
			"other_fees": 12335,
			"total_due": 26150,
			"gross_tuition_fee": 29340,
			"due_dates": ["Upon Enrollment", "July 25, 2025", "Oct 25, 2025", "Jan 26, 2026"],
			"due_amounts": [26150, 8215, 8215, 8215]
		  },
		  "GX": {
			"tuition_fee": 8215,
			"miscellaneous_fees": 5600,
			"other_fees": 14135,
			"total_due": 27950,
			"gross_tuition_fee": 29340,
			"due_dates": ["Upon Enrollment", "July 25, 2025", "Oct 25, 2025", "Jan 26, 2026"],
			"due_amounts": [27950, 8215, 8215, 8215]
		  },
		  "GY": {
			"tuition_fee": 6630,
			"miscellaneous_fees": 5600,
			"other_fees": 12335,
			"total_due": 24565,
			"gross_tuition_fee": 23679,
			"due_dates": ["Upon Enrollment", "July 25, 2025", "Oct 25, 2025", "Jan 26, 2026"],
			"due_amounts": [24565, 6630, 6630, 6630]
		  },
		  "GZ": {
			"tuition_fee": 6630,
			"miscellaneous_fees": 5600,
			"other_fees": 14835,
			"total_due": 27065,
			"gross_tuition_fee": 23679,
			"due_dates": ["Upon Enrollment", "July 25, 2025", "Oct 25, 2025", "Jan 26, 2026"],
			"due_amounts": [27065, 6630, 6630, 6630]
		  }
		}
	  };
	return PayCQuarterly;
});