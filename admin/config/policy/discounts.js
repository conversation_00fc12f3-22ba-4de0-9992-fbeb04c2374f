define([], function(){
	var discounts = {
    "EARLY_BIRD_DISCOUNT": {
      "enroll_on_before": "APR 30 2025",
      "amount": 5,
      "unit": "PERCENT",
      "fee": "TUI",
      "apply_on": "UPON_ENROL",
      "policy": "Apply a 5% discount on tuition fees on CASH PLANS for students who enroll on or before APR 30 2025 "
    },
    "SIBLING_DISCOUNT_1ST": {
      "sibling_order": 1,
      "amount": 0,
      "unit": "PERCENT",
      "fee": "TUI",
      "apply_on": "LAST_DUE",
      "policy": "Apply a percentage discount on tuition fees for every siblings enrolled based on their order."
    },
    "SIBLING_DISCOUNT_2ND": {
      "sibling_order": 2,
      "amount": 5,
      "unit": "PERCENT",
      "fee": "TUI",
      "apply_on": "LAST_DUE",
      "policy": "Apply a percentage discount on tuition fees for every siblings enrolled based on their order."
    },
    "SIBLING_DISCOUNT_3RD": {
      "sibling_order": 3,
      "amount": 10,
      "unit": "PERCENT",
      "fee": "TUI",
      "apply_on": "LAST_DUE",
      "policy": "Apply a percentage discount on tuition fees for the third sibling enrolled."
    },
    "SIBLING_DISCOUNT_4TH": {
      "sibling_order": 4,
      "amount": 15,
      "unit": "PERCENT",
      "fee": "TUI",
      "apply_on": "LAST_DUE",
      "policy": "Apply a percentage discount on tuition fees for the fourth sibling enrolled."
    },
    "SIBLING_DISCOUNT_5TH": {
      "sibling_order": 5,
      "amount": 20,
      "unit": "PERCENT",
      "fee": "TUI",
      "apply_on": "LAST_DUE",
      "policy": "Apply a percentage discount on tuition fees for the fifth sibling enrolled."
    },
    "REFERRAL_DISCOUNT": {
      "has_referral": true,
      "amount": 700,
      "unit": "PESO",
      "fee": "TUI",
      "apply_on": "LAST_DUE",
      "policy": "Apply a fixed amount discount in pesos on tuition fees for students who were referred."
    },
    "SPECIAL_DISCOUNT": {
      "requires_approval": true,
      "amount": "VARIES",
      "unit": "PERCENT",
      "fee": "TUI",
      "apply_on": "LAST_DUE",
      "policy": "Apply a varying percentage discount on tuition fees, subject to approval."
    },
    "EMPLOYEE_CHILD_DISCOUNT_1YR": {
      "employee_service_years": 1,
      "amount": 15,
      "unit": "PERCENT",
      "fee": "TUI",
      "apply_on": "LAST_DUE",
      "policy": "Apply a percentage discount on tuition fees for children of employees with at least 1 year of service."
    },
    "EMPLOYEE_CHILD_DISCOUNT_2YR": {
      "employee_service_years": 2,
      "amount": 20,
      "unit": "PERCENT",
      "fee": "TUI",
      "apply_on": "LAST_DUE",
      "policy": "Apply a percentage discount on tuition fees for children of employees with at least 2 years of service."
    },
    "EMPLOYEE_CHILD_DISCOUNT_3YR": {
      "employee_service_years": 3,
      "amount": 30,
      "unit": "PERCENT",
      "fee": "TUI",
      "apply_on": "LAST_DUE",
      "policy": "Apply a percentage discount on tuition fees for children of employees with at least 3 years of service."
    },
    "EMPLOYEE_CHILD_DISCOUNT_4YR": {
      "employee_service_years": 4,
      "amount": 100,
      "unit": "PERCENT",
      "fee": "TUI",
      "apply_on": "LAST_DUE",
      "policy": "Apply a percentage discount on tuition fees for children of employees with at least 4 years of service."
    },
    "SPORTS_SCHOLARSHIP": {
      "achieved_rank": "2ND_PLACE_OR_HIGHER",
      "event_level": "PROVINCIAL",
      "amount": 15,
      "unit": "PERCENT",
      "fee": "TUI",
      "apply_on": "LAST_DUE",
      "policy": "Apply a 15% discount on tuition fees for students who achieved 2nd place or higher in a provincial sports event."
    },
    "ACADEMIC_SCHOLARSHIP": {
      "policy": "Apply a full/half tuition fee discount for students from public and private schools in grades 7-12, with special rates for miscellaneous fees."
    },
    "ACADEMIC_SCHOLARSHIP_FULL_PUBLIC": {
      "student_source": "PUBLIC",
      "grade_level": ["G7", "G8", "G9", "GX", "GY", "GZ"],
      "amount": 100,
      "unit": "PERCENT",
      "fee": "TUI",
      "misc_fees": "SPECIAL_RATES",
      "apply_on": "LAST_DUE",
      "policy": "Apply a full tuition fee discount for students from public schools in grades 7-12, with special rates for miscellaneous fees."
    },
    "ACADEMIC_SCHOLARSHIP_FULL_PRIVATE": {
      "student_source": "PRIVATE",
      "grade_level": ["G7", "G8", "G9", "GX", "GY", "GZ"],
      "amount": 100,
      "unit": "PERCENT",
      "fee": "TUI",
      "misc_fees": "REGULAR_RATES",
      "apply_on": "LAST_DUE",
      "policy": "Apply a full tuition fee discount for students from private schools in grades 7-12, with regular rates for miscellaneous fees."
    },
    "ACADEMIC_SCHOLARSHIP_PARTIAL_PUBLIC": {
      "student_source": "PUBLIC",
      "grade_level": ["G7", "G8", "G9", "GX", "GY", "GZ"],
      "amount": 50,
      "unit": "PERCENT",
      "fee": "TOTAL",
      "misc_fees": "INCLUDED",
      "apply_on": "LAST_DUE",
      "policy": "Apply a partial discount on the total fees for students from public schools in grades 7-12, with miscellaneous fees included."
    },
    "ACADEMIC_SCHOLARSHIP_PARTIAL_PRIVATE": {
      "student_source": "PRIVATE",
      "grade_level": ["G7", "G8", "G9", "GX", "GY", "GZ"],
      "amount": 50,
      "unit": "PERCENT",
      "fee": "TUI",
      "apply_on": "LAST_DUE",
      "policy": "Apply a partial discount on tuition fees for students from private schools in grades 7-12."
    },
    "ESC_GRANT": {
      "applicable_levels": ["G7", "G8", "G9", "G10"],
      "amount": 9000,
      "unit": "PESO",
      "fee": "TUI",
      "apply_on": "LAST_DUE",
      "policy": "Apply a fixed amount grant in pesos on tuition fees for students in grades 7-10."
    },
    "SHS_VOUCHER_PUBLIC": {
      "applicable_levels": ["GY", "GZ"],
      "amount": 17500,
      "unit": "PESO",
      "fee": "TUI",
      "apply_on": "LAST_DUE",
      "policy": "Apply a fixed amount voucher in pesos on tuition fees for students in grades 11-12 from public schools."
    },
    "SHS_VOUCHER_PRIVATE": {
      "applicable_levels": ["GY", "GZ"],
      "amount": 14000,
      "unit": "PESO",
      "fee": "TUI",
      "apply_on": "LAST_DUE",
      "policy": "Apply a fixed amount voucher in pesos on tuition fees for students in grades 11-12 from private schools."
    },
    "WAIVED_REGISTRATION_FEE": {
      "first_n_students": 300,
      "amount": 600,
      "unit": "PESO",
      "fee": "REGISTRATION",
      "apply_on": "UPON_ENROL",
      "policy": "The waived registration fee of ₱600 is available to the first 300 students who successfully make a reservation for SY 2025-2026."
    }
  };
	return discounts;
});