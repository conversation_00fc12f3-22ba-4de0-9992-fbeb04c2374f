<?php
	$contollerPrefix = 'SMC';
	if(isset($_MODULE)){
		if($_MODULE=='payments'){
			$contollerPrefix = 'OPC';
		}
	}else{
		$_MODULE='registrations';
	}
?>

<a-col size="4" align="right">
	<m-formgroup  ng-model="<?php echo $contollerPrefix; ?>.DateFilterFrom"  ng-disabled="<?php echo $contollerPrefix; ?>.showSearch"  label="From" hide-label="true" type="'date'" class="filter-ui"></m-formgroup>
	<span> - </span>
	<m-formgroup  ng-model="<?php echo $contollerPrefix; ?>.DateFilterTo"  ng-disabled="<?php echo $contollerPrefix; ?>.showSearch"    label="To" hide-label="true" type="'date'" class="filter-ui"></m-formgroup>
</a-col>
<a-col size="2">
	<m-formgroup  ng-model="<?php echo $contollerPrefix; ?>.DisplayFilter" options="<?php echo $contollerPrefix; ?>.AppStatuses"  label="Status" hide-label="true" class="filter-ui full-width"></m-formgroup>
</a-col>
<a-col size="2" align="right">
	<a-button type="primary" class="filter-ui hidden-sm hidden-xs" ng-click="<?php echo $contollerPrefix; ?>.filterRecords()" ng-disabled="<?php echo $contollerPrefix; ?>.isLoading" style="margin-left:-5px;">
		<a-glyph icon="filter"></a-glyph> Filter
	</a-button>
	<a-button type="primary btn-block" class="filter-ui hidden-md hidden-lg" ng-click="<?php echo $contollerPrefix; ?>.filterRecords()" ng-disabled="<?php echo $contollerPrefix; ?>.isLoading">
		<a-glyph icon="filter"></a-glyph>  Filter Items
	</a-button>
	<a-button type="default" class="filter-ui  hidden-sm hidden-xs"  ng-click="<?php echo $contollerPrefix; ?>.clearFilter()" ng-disabled="<?php echo $contollerPrefix; ?>.isLoading || !<?php echo $contollerPrefix; ?>.isRecordsFiltered ">
		<a-glyph icon="remove"></a-glyph>
	</a-button>
<?php if($_MODULE!='payments'):?>
	<a-button class="filter-ui hidden-sm hidden-xs" ng-class={'btn-warning':<?php echo $contollerPrefix; ?>.showSearch} ng-click="<?php echo $contollerPrefix; ?>.toggleSeach()">
		<a-glyph icon="search"></a-glyph>
	</a-button>
<?php endif; ?>
</a-col>
<a-col>
	<a-row ng-show="<?php echo $contollerPrefix; ?>.showSearch" style="margin-top:5px;">
		<a-col size="10">
			<m-formgroup ng-model="<?php echo $contollerPrefix; ?>.StudentSearch" placeholder="Search online registration by name or ref no." 	hide-label="true" style="margin-top:5px;"></m-formgroup>
		</a-col>
		<a-col size="2">
			<a-button type="default-block" style="margin-left:-2.5px;margin-top:5px;" ng-click="<?php echo $contollerPrefix; ?>.searchRecords()">Search</a-button>
		</a-col>
	</a-row>


</a-col>
