<style>
	.right-panel{
		position: relative;
	}
	.discount-title{
		float: left;
		margin:5px 0px;
	}
	.toggle-button{
		float: right;
	}
	.test-mode .a-header{
		background-color:rgb(118 26 139);
		color:white;
		border:1px solid rgb(118 26 139);
		border-bottom:none;
	}
	.test-mode .list-group-canvas {
		border:1px solid rgb(118 26 139);
	}
	.test-mode  .a-header .btn-default, .test-mode  .a-header .btn-default:hover, .test-mode  .a-header .btn-default:focus, .test-mode  .a-header .btn-default:active{
		background-color:rgb(118 26 139);
		border-color:white;
		color:white;
	}
	.test-mode .a-header .btn-default>span{
		color:white;
	}
	.dl-horizontal>dt{
		text-align: left;
	}
	.alert-request .alert{
		padding: 6px 15px;
	}
	.dl-horizontal.assessment-summary-list> dt{
		width:400px;
		white-space:break-spaces;
		margin-bottom:5px;
	}
	.modal.confirmed .modal-header{
		background-color:#469f46;
		color:white;
		border:1px solid #469f46;
		border-bottom:none;
	}
	div.notify-email .form-group,
	div.notify-email .email-label,
	div.notify-email .form-control{
		display:inline-block;
		height:auto;
		margin:0px
	}
	div.notify-email .email-label label{
		width:150px;
	}
	div.notify-email .form-control[type="text"]{
		width:300px;
	}
</style>
<div ng-controller="StudentMainController as SMC" ng-init="SMC.init('confirmation')">
	<div ng-controller="AssessmentController as ASC" ng-init="ASC.init()">
		<a-module class="{{ASC.AppMode =='TEST' ? 'test-mode' : ''}}">
			<a-container>
				<a-header>
						<a-row>
							<a-col size="4">


								<h4><a-glyph icon="pencil"></a-glyph>  Student Assessment</h4>

							</a-col>
							<a-col size="8" align="right">
								<a-button  ng-click="ASC.viewRequests()" ng-disabled="ASC.disableViewRequests">
									<span ng-show="!ASC.disableViewRequests" class="label label-danger">{{ASC.RequestsCounter}}</span> Requests
								</a-button>
								<a-button  ng-click="ASC.toggleAppMode()" ng-disabled="ASC.StudentObj.id">
									<a-glyph icon="{{ASC.AppMode =='TEST' ? 'remove-sign' : 'play-circle'}}"></a-glyph> Test Mode
								</a-button>

							</a-col>
						</a-row>
				</a-header>
				<a-canvas>
					<a-content>
						<a-row>
							<a-col size="8">
								<a-row>
									<a-col size="9" ng-if="ASC.AppMode =='NORMAL'">
											<div class="form-group">
												<label for="">Student</label>
												<m-search-entity ng-model="ASC.StudentObj" endpoint="'students/search'" display-field="'display_name'" obj-fields="ASC.StuObjFields" is-large="true"></m-search-entity>
											</div>

									</a-col>
									<a-col size="9" ng-if="ASC.AppMode =='TEST'">
											<m-formgroup ng-model="ASC.TestModeStudentType"
												label="Student Type"
												options="[
													{id:'NEW', name:'New Student'},
													{id:'OLD', name:'Old Student'}
												]"
												select-prefix="Select Student Type"
												size="'input-lg'">
											</m-formgroup>
									</a-col>
									<a-col size="3">
											<m-formgroup ng-model="ASC.Assessment.year_level_id"
												label="Incoming Year Level"
												options="ASC.YearLevels"
												option-label="description"
												select-prefix="Select Level"
												ng-disabled="!ASC.Assessment.student_id"
												size="'input-lg'">
											</m-formgroup>

									</a-col>
								</a-row>

							</a-col>
							<a-col size="4" class="right-panel">
								<a-row>
									<a-col size="5">
									<m-formgroup ng-model="ASC.Assessment.esp" label="School Year" ng-disabled="!ASC.Assessment.student_id" options="ASC.SchoolYears" option-label="label" select-prefix="Select School Year" ></m-formgroup>

									</a-col>
									<a-col size="7">
										<m-formgroup  ng-disabled="!ASC.Assessment.year_level_id"   ng-model="ASC.Assessment.payment_plan_id" label="Payment Plan" options="ASC.PaymentPlans" ng-disabled="!ASC.Assessment.student_id"></m-formgroup>

									</a-col>
								</a-row>
							</a-col>
							</a-row>
						<a-row>
							<a-col size="8">
								<div class="pull-right">

								</div>
								<uib-tabset type="tabs" active="ASC.ActiveTabIndex">
									<uib-tab index="0" heading="[1] Fees">
										<div class="well" ng-if="!ASC.StudentObj.id">

											<div class="help-text">
												<br><br>
												<h3><a-glyph icon="user"></a-glyph></h3>

												<h4>No Student selected</h4>
												<p>Search student to begin.</p>
												<br>
												<br><br>
											</div>

										</div>
										<div ng-if="ASC.StudentObj.id">
										<div class="alert alert-warning" ng-show="!ASC.Assessment.year_level_id">
											<a-glyph icon="warning"></a-glyph>No Fees to display. Please select <b>Incoming Year Level</b> first.
										</div>
											<m-formgroup ng-disabled="!ASC.Assessment.year_level_id" ng-model="ASC.Assessment.payment_plan_id" label="Payment Plan" type="'btn-group'" ng-disabled="!ASC.StudentObj.id" options="ASC.PaymentPlans"></m-formgroup>
										<a-table headers="ASC.TuitionHeaders" props="ASC.TuitionProps" data="ASC.TuitionData" is-preload="false"></a-table>

										<a-button  type="danger outline" ng-click="ASC.cancelAssessment()" ng-disabled="!ASC.Assessment.student_id">
											Reset Assessment
										</a-button>
											</div>
								</uib-tab>
									<uib-tab index="1" heading="[2] Schedule" disable="!ASC.StudentObj.id" >
										<div class="alert alert-warning" ng-show="!ASC.Assessment.year_level_id">
											<a-glyph icon="warning"></a-glyph>No Schedule to display. Please select <b>Incoming Year Level</b> first.
										</div>
										<m-formgroup ng-disabled="!ASC.Assessment.year_level_id"  ng-model="ASC.Assessment.payment_plan_id" label="Payment Plan" type="'btn-group'" ng-disabled="!ASC.StudentObj.id" options="ASC.PaymentPlans"></m-formgroup>
										<a-table headers="ASC.PaySchedHeaders" props="ASC.PaySchedProps" data="ASC.PaySchedData"  is-preload="false"></a-table>
										<a-button  type="danger outline" ng-click="ASC.cancelAssessment()" ng-disabled="!ASC.Assessment.student_id">
											Reset Assessment
										</a-button>
									</uib-tab>

									<uib-tab index="2" heading="[3] Adjustments"  disable="!ASC.StudentObj.id"  >
										<div class="alert alert-warning" ng-show="!ASC.Assessment.year_level_id">
											<a-glyph icon="warning"></a-glyph>No Adjustments to compute. Please select <b>Incoming Year Level</b> first.
										</div>
										<m-formgroup ng-disabled="!ASC.Assessment.year_level_id"  ng-model="ASC.Assessment.payment_plan_id" label="Payment Plan" type="'btn-group'" ng-disabled="!ASC.StudentObj.id" options="ASC.PaymentPlans"></m-formgroup>
										<!-- Reservation Fee Deduction -->
										<a-row class="mb-4">
											<a-col size="12">
												<div class="panel  {{ASC.ReservationInvalid?'panel-danger':'panel-default'}} ">
													<div class="panel-heading">
														<div class="discount-title">
																<a-glyph icon="credit-card"></a-glyph> Reservation Fee
														</div>
														<div class="toggle-button">
															<a-button size="sm"
															type=" {{ASC.ReservationInvalid?'danger':'default'}} "
															ng-click="ASC.showReservationFee = !ASC.showReservationFee">
																<a-glyph icon="chevron-{{ASC.showReservationFee ? 'up' : 'down'}}"></a-glyph>
															</a-button>
														</div>
														<div class="clearfix"></div>
													</div>
													<div class="panel-body" ng-if="ASC.showReservationFee">
														<a-row>
															<a-col size="4">
																<m-formgroup ng-model="ASC.Assessment.has_reservation_fee"
																	label="Deduct Reservation Fee?"
																	type="'yesno'"
																	select-prefix="Deduct Reservation Fee?"
																	help-text="'Deduct reservation fee from total assessment'">
																</m-formgroup>
															</a-col>
															<a-col size="4">
																<m-formgroup ng-model="ASC.Assessment.registration_no"
																	label="Registration No."
																	ng-disabled="ASC.Assessment.has_reservation_fee !== 'Y'">
																</m-formgroup>
															</a-col>
															<a-col size="4">
																<m-formgroup ng-model="ASC.Assessment.reservation_fee_amount"
																	label="Reservation Fee Amount"
																	type="'number'"
																	ng-disabled="ASC.Assessment.has_reservation_fee !== 'Y'">
																</m-formgroup>
															</a-col>
														</a-row>
														<p class="alert alert-info" ng-hide="ASC.ReservationInvalid">The reservation fee paid during registration will be deducted from the total assessment amount.</p>
														<p class="alert alert-danger" ng-show="ASC.ReservationInvalid">The related Registration No. was found but not yet <b>APPROVED</b> or <b>VALIDATED</b>. </p>
													</div>
												</div>
											</a-col>
										</a-row>
										<!-- Textbook and School Supplies Section -->
										<a-row class="mb-4">
											<a-col size="12">
												<div class="panel panel-default">
													<div class="panel-heading">
														<div class="discount-title">
															<a-glyph icon="book"></a-glyph> Textbooks and School Supplies
														</div>
														<div class="toggle-button">
															<a-button size="sm" ng-click="ASC.showTextbookSupplies = !ASC.showTextbookSupplies">
																<a-glyph icon="chevron-{{ASC.showTextbookSupplies ? 'up' : 'down'}}"></a-glyph>
															</a-button>
														</div>
														<div class="clearfix"></div>
													</div>
													<div class="panel-body" ng-if="ASC.showTextbookSupplies">
														<a-row>
															<a-col size="6">
																<m-formgroup ng-model="ASC.Assessment.purchase_textbooks_supplies"
																	label="Purchase textbooks and school supplies?"
																	type="'yesno'"
																	help-text="'Select whether you want to include textbooks and school supplies in your assessment'">
																</m-formgroup>
															</a-col>
															<a-col size="6" >
																<m-formgroup ng-model="ASC.Assessment.textbooks_supplies_option"
																	label="Purchase Option"
																	options="[
																		{id:'COMPLETE', name:'Complete set'},
																		{id:'SELECTED', name:'Selected items'}
																	]"
																	select-prefix="Select Option"
																	ng-disabled="ASC.Assessment.purchase_textbooks_supplies === 'N'">
																</m-formgroup>
															</a-col>
														</a-row>

														<!-- Textbook and supplies amounts -->
														<a-row>
															<a-col size="6">
																<!-- For Selected items: editable field -->
																<m-formgroup ng-disabled="ASC.Assessment.textbooks_supplies_option !== 'SELECTED' || ASC.Assessment.purchase_textbooks_supplies === 'N'"
																	ng-model="ASC.Assessment.custom_textbook_amount"
																	label="Textbooks Amount"
																	type="'number'">
																</m-formgroup>

															</a-col>
															<a-col size="6">
																<!-- For Selected items: editable field -->
																<m-formgroup ng-disabled="ASC.Assessment.textbooks_supplies_option !== 'SELECTED' || ASC.Assessment.purchase_textbooks_supplies === 'N'"
																	ng-model="ASC.Assessment.custom_supplies_amount"
																	label="School Supplies Amount"
																	type="'number'">
																</m-formgroup>
															</a-col>
														</a-row>

														<p class="alert alert-info">Textbooks and school supplies are always paid in full upon enrollment regardless of payment plan.</p>
													</div>
												</div>
											</a-col>
										</a-row>

										<!-- Waived Registration Fee -->
										<a-row class="mb-4">
											<a-col size="12">
												<div class="panel panel-default">
													<div class="panel-heading">
														<div class="discount-title">
																<a-glyph icon="star"></a-glyph> Waived Registration Fee
														</div>
														<div class="toggle-button">
															<a-button size="sm" ng-click="ASC.showWaivedRegistrationFee = !ASC.showWaivedRegistrationFee">
																<a-glyph icon="chevron-{{ASC.showWaivedRegistrationFee ? 'up' : 'down'}}"></a-glyph>
															</a-button>
														</div>
														<div class="clearfix"></div>
													</div>
													<div class="panel-body" ng-if="ASC.showWaivedRegistrationFee">
														<a-row>
															<a-col size="6">
																<m-formgroup ng-model="ASC.Assessment.has_waived_registration_fee"
																	label="Apply Waived Registration Fee"
																	type="'yesno'"
																	select-prefix="Apply Waived Registration Fee?"
																	help-text="'₱600 waived registration fee for first 300 students'">
																</m-formgroup>
															</a-col>
															<a-col size="6">
																<m-formgroup ng-model="ASC.Assessment.waived_registration_fee_discount"
																	label="Discount Amount"
																	type="'number'"
																	ng-disabled="true"
																	class="text-success">
																</m-formgroup>
															</a-col>
														</a-row>
														<p class="alert alert-info">{{ASC.DiscountText.WAIVED_REGISTRATION_FEE}}</p>
													</div>
												</div>
											</a-col>
										</a-row>

										<!-- Early Bird Discount -->
										<a-row class="mb-4">
											<a-col size="12">
												<div class="panel" ng-class="{'panel-default': ASC.Assessment.early_bird_available !== false, 'panel-danger': ASC.Assessment.early_bird_available === false}">
													<div class="panel-heading">
														<div class="discount-title">
																<a-glyph icon="calendar"></a-glyph> Early Bird Discount
														</div>
														<div class="toggle-button">
															<a-button size="sm"
																ng-class="{'btn-danger': ASC.Assessment.early_bird_available === false}"
																ng-click="ASC.showEarlyBird = !ASC.showEarlyBird">
																<a-glyph icon="chevron-{{ASC.showEarlyBird ? 'up' : 'down'}}"></a-glyph>
															</a-button>
														</div>
														<div class="clearfix"></div>
													</div>
													<div class="panel-body" ng-if="ASC.showEarlyBird">
														<a-row>
															<a-col size="8">
																<m-formgroup ng-model="ASC.Assessment.early_bird"
																	label="Apply Early Bird"
																	type="'yesno'"
																	help-text="ASC.Assessment.early_bird_discount_percent + '% discount if enrolled before ' + ASC.Assessment.early_bird_deadline"
																	ng-disabled="ASC.Assessment.payment_plan_id != 'PLANA' || ASC.Assessment.early_bird_available === false">
																</m-formgroup>
															</a-col>
															<a-col size="4">
																<m-formgroup ng-model="ASC.Assessment.early_bird_discount"
																	label="Discount Amount"
																	type="'number'"
																	ng-disabled="true"
																	class="text-success">
																</m-formgroup>
															</a-col>
														</a-row>
														<!-- Show alert-danger if early bird is expired -->
														<p class="alert alert-danger" ng-if="ASC.Assessment.early_bird_available === false">
															<a-glyph icon="warning-sign"></a-glyph> {{ASC.Assessment.early_bird_message || 'Early Bird discount is no longer available. The deadline has passed.'}}
														</p>
														<!-- Show regular info if early bird is available -->
														<p class="alert alert-info" ng-if="ASC.Assessment.early_bird_available !== false">
															<a-glyph icon="info-sign"></a-glyph> Early Bird discount ({{ ASC.Assessment.early_bird_discount_percent }}% off tuition fee) is available until {{ ASC.Assessment.early_bird_deadline }}.
															<span ng-if="ASC.Assessment.payment_plan_id != 'PLANA'"><strong>Note:</strong> This discount is only applicable for CASH payment plans.</span>
														</p>
													</div>
												</div>
											</a-col>
										</a-row>

										<!-- Sibling Discount -->
										<a-row class="mb-4">
											<a-col size="12">
												<div class="panel panel-default">
													<div class="panel-heading">
														<div class="discount-title">
															<a-glyph icon="user"></a-glyph> Sibling Discount
														</div>
														<div class="toggle-button">
															<a-button size="sm" ng-click="ASC.showSiblingDiscount = !ASC.showSiblingDiscount">
																<a-glyph icon="chevron-{{ASC.showSiblingDiscount ? 'up' : 'down'}}"></a-glyph>
															</a-button>
														</div>
														<div class="clearfix"></div>
													</div>
													<div class="panel-body" ng-if="ASC.showSiblingDiscount">
														<a-row>
															<a-col size="3">
																<m-formgroup ng-model="ASC.Assessment.has_child_discount"
																	label="Apply Sibling Discount"
																	type="'yesno'"
																	help-text="'Discounts increase with each sibling (5% to 20%)'">
																</m-formgroup>
															</a-col>
															<a-col size="5" >
																<m-formgroup ng-model="ASC.Assessment.sibling_order"
																	label="Sibling Order"
																	options="[
																		{id:1,name:'1st Child (0%) - Youngest'},
																		{id:2,name:'2nd Child (5%)'},
																		{id:3,name:'3rd Child (10%)'},
																		{id:4,name:'4th Child (15%)'},
																		{id:5,name:'5th Child (20%) - Eldest'}
																	]"
																	select-prefix="Select Order"
																	ng-disabled="ASC.Assessment.has_child_discount !== 'Y'">
																</m-formgroup>
															</a-col>
															<a-col size="4">
																<m-formgroup ng-model="ASC.Assessment.sibling_order_discount"
																	label="Discount Amount"
																	type="'number'"
																	ng-disabled="true"
																	class="text-success">
																</m-formgroup>
															</a-col>
														</a-row>
														<p class="alert alert-info">{{ASC.DiscountText.SIBLING_DISCOUNT_2ND}}</p>
													</div>
												</div>
											</a-col>
										</a-row>

										<!-- Employee Child Discount -->
										<a-row class="mb-4">
											<a-col size="12">
												<div class="panel panel-default">
													<div class="panel-heading">
														<div class="discount-title">
															<a-glyph icon="briefcase"></a-glyph> Employee Discount
														</div>
														<div class="toggle-button">
															<a-button size="sm" ng-click="ASC.showEmployeeDiscount = !ASC.showEmployeeDiscount">
																<a-glyph icon="chevron-{{ASC.showEmployeeDiscount ? 'up' : 'down'}}"></a-glyph>
															</a-button>
														</div>
														<div class="clearfix"></div>
													</div>
													<div class="panel-body" ng-if="ASC.showEmployeeDiscount">
														<a-row>
															<a-col size="3">
																<m-formgroup ng-model="ASC.Assessment.is_employee_child"
																	label="Apply EC Discount"
																	type="'yesno'"
																	help-text="'Discounts increase with years of service (15% to 100%)'">
																</m-formgroup>
															</a-col>
															<a-col size="5">
																<m-formgroup ng-model="ASC.Assessment.employee_years"
																	label="Years of Service"
																	options="[
																		{id:1,name:'1 Year (15%)'},
																		{id:2,name:'2 Years (20%)'},
																		{id:3,name:'3 Years (30%)'},
																		{id:4,name:'4 Years (100%)'}
																	]"
																	select-prefix="Select Years"
																	ng-disabled="ASC.Assessment.is_employee_child !== 'Y'">
																</m-formgroup>
															</a-col>
															<a-col size="4">
																<m-formgroup ng-model="ASC.Assessment.employee_child_discount"
																	label="Discount Amount"
																	type="'number'"
																	ng-disabled="true"
																	class="text-success">
																</m-formgroup>
															</a-col>
														</a-row>
														<p class="alert alert-info">{{ASC.DiscountText.EMPLOYEE_CHILD_DISCOUNT_1YR}}</p>
													</div>
												</div>
											</a-col>
										</a-row>

										<!-- ESC/Voucher Options -->
										<a-row class="mb-4" ng-if="ASC.isEscEligible || ASC.isVoucherEligible">
											<a-col size="12">
												<div class="panel panel-default">
													<div class="panel-heading">
														<div class="discount-title">
															<a-glyph icon="education"></a-glyph> Government Subsidies
														</div>
														<div class="toggle-button">
															<a-button size="sm" ng-click="ASC.showEscVoucher = !ASC.showEscVoucher">
																<a-glyph icon="chevron-{{ASC.showEscVoucher ? 'up' : 'down'}}"></a-glyph>
															</a-button>
														</div>
														<div class="clearfix"></div>
													</div>
													<div class="panel-body" ng-if="ASC.showEscVoucher">
														<a-row>
															<a-col size="8" ng-if="ASC.isVoucherEligible">
																<m-formgroup ng-model="ASC.Assessment.student_source"
																	label="Previous School Type"
																	options="[
																		{id:'PUBLIC',name:'Public School (₱17,500)'},
																		{id:'PRIVATE',name:'Private School (₱14,000)'}
																	]"
																	select-prefix="Select Source">
																</m-formgroup>
															</a-col>
															<a-col size="8" ng-if="ASC.isEscEligible">
																<m-formgroup ng-model="ASC.Assessment.esc_grantee"
																	label="ESC Grantee"
																	type="'yesno'"
																	help-text="'₱9,000 grant for Grade 7-10'">
																</m-formgroup>
															</a-col>
															<a-col size="4" ng-show="ASC.isEscEligible">
																<m-formgroup ng-model="ASC.Assessment.esc_discount"
																	label="ESC Grant Amount"
																	type="'number'"
																	ng-disabled="true"
																	class="text-success">
																</m-formgroup>
															</a-col>
															<a-col size="4" ng-show="ASC.isVoucherEligible">
																<m-formgroup ng-model="ASC.Assessment.voucher_discount"
																	label="Voucher Amount"
																	type="'number'"
																	ng-disabled="true"
																	class="text-success">
																</m-formgroup>
															</a-col>
														</a-row>
														<p class="alert alert-info" ng-if="ASC.isEscEligible">{{ASC.DiscountText.ESC_GRANT}}</p>
														<p class="alert alert-info" ng-if="ASC.isVoucherEligible && ASC.Assessment.student_source == 'PUBLIC'">{{ASC.DiscountText.SHS_VOUCHER_PUBLIC}}</p>
														<p class="alert alert-info" ng-if="ASC.isVoucherEligible && ASC.Assessment.student_source == 'PRIVATE'">{{ASC.DiscountText.SHS_VOUCHER_PRIVATE}}</p>
													</div>
												</div>
											</a-col>
										</a-row>

										<!-- Referral Discount -->
										<a-row class="mb-4">
											<a-col size="12">
												<div class="panel panel-default">
													<div class="panel-heading">
														<div class="discount-title">
															<a-glyph icon="tags"></a-glyph> &nbsp;Referral Discount
														</div>
														<div class="toggle-button">
															<a-button size="sm" ng-click="ASC.showReferralDiscount = !ASC.showReferralDiscount">
																<a-glyph icon="chevron-{{ASC.showReferralDiscount ? 'up' : 'down'}}"></a-glyph>
															</a-button>
														</div>
														<div class="clearfix"></div>
													</div>
													<div class="panel-body" ng-if="ASC.showReferralDiscount">
														<a-row>
															<a-col size="8">
																<m-formgroup ng-model="ASC.Assessment.has_referral"
																	label="Apply Referral Discount"
																	type="'yesno'"
																	help-text="'₱700 discount for referred students'">
																</m-formgroup>
															</a-col>
															<a-col size="4">
																<m-formgroup ng-model="ASC.Assessment.referral_discount"
																	label="Discount Amount"
																	type="'number'"
																	ng-disabled="true"
																	class="text-success">
																</m-formgroup>
															</a-col>
														</a-row>
														<p class="alert alert-info">{{ASC.DiscountText.REFERRAL_DISCOUNT}}</p>
													</div>
												</div>
											</a-col>
										</a-row>

										<!-- Sport Scholarship Section -->
										<a-row class="mb-4">
											<a-col size="12">
												<div class="panel panel-default">
													<div class="panel-heading">
														<div class="discount-title">
															<a-glyph icon="tower"></a-glyph> Sports Scholarship
														</div>
														<div class="toggle-button">
															<a-button size="sm" ng-click="ASC.showSportScholarship = !ASC.showSportScholarship">
																<a-glyph icon="chevron-{{ASC.showSportScholarship ? 'up' : 'down'}}"></a-glyph>
															</a-button>
														</div>
														<div class="clearfix"></div>
													</div>
													<div class="panel-body" ng-if="ASC.showSportScholarship">
														<a-row>
															<a-col size="8">
																<m-formgroup ng-model="ASC.Assessment.has_sport_scholarship"
																	label="Apply Sports Scholarship"
																	type="'yesno'"
																	help-text="'Discount for students with sports scholarships'">
																</m-formgroup>
															</a-col>
															<a-col size="4">
																<m-formgroup ng-model="ASC.Assessment.sport_scholarship_discount"
																	label="Discount Amount"
																	type="'number'"
																	ng-disabled="true"
																	class="text-success">
																</m-formgroup>
															</a-col>
														</a-row>
														<p class="alert alert-info">{{ASC.DiscountText.SPORTS_SCHOLARSHIP}}</p>
													</div>
												</div>
											</a-col>
										</a-row>

										<!-- Academic Scholarship Section -->
										<a-row class="mb-4" ng-if="ASC.isAcademicScholarshipEligible">
											<a-col size="12">
												<div class="panel panel-default">
													<div class="panel-heading">
														<div class="discount-title">
															<a-glyph icon="book"></a-glyph> Academic Scholarship
														</div>
														<div class="toggle-button">
															<a-button size="sm" ng-click="ASC.showAcademicScholarship = !ASC.showAcademicScholarship">
																<a-glyph icon="chevron-{{ASC.showAcademicScholarship ? 'up' : 'down'}}"></a-glyph>
															</a-button>
														</div>
														<div class="clearfix"></div>
													</div>
													<div class="panel-body" ng-if="ASC.showAcademicScholarship">
														<a-row>
															<a-col size="4">
																<m-formgroup ng-model="ASC.Assessment.student_source"
																	label="Previous School Type"
																	options="[
																		{id:'PUBLIC', name:'Public School'},
																		{id:'PRIVATE', name:'Private School'}
																	]"
																	select-prefix="Select Source">
																</m-formgroup>
															</a-col>
															<a-col size="4">
																<m-formgroup ng-model="ASC.Assessment.academic_scholarship_type"
																	label="Scholarship Type"
																	options="[
																		{id:'FULL', name:'Full Scholarship'},
																		{id:'PARTIAL', name:'Partial Scholarship'}
																	]"
																	select-prefix="Select Type">
																</m-formgroup>
															</a-col>
															<a-col size="4">
																<m-formgroup ng-model="ASC.Assessment.academic_scholarship_discount"
																	label="Discount Amount"
																	type="'number'"
																	ng-disabled="true"
																	class="text-success">
																</m-formgroup>
															</a-col>
														</a-row>
														<a-row>
															<a-col>
																<uib-tabset type="tabs">
																	<uib-tab index="0" heading="Fees">
																		<m-table-edit
																			headers="ASC.ScholarshipFeeHeaders"
																			props="ASC.ScholarshipFeeProps"
																			data="ASC.ScholarshipFeeData"
																			inputs="ASC.ScholarshipFeeInputs"
																			allow-add="false"
																			allow-del="false"
																			on-edit-save="ASC.updateScholarshipFees">
																		</m-table-edit>
																	</uib-tab>
																	<uib-tab index="1" heading="Schedule">
																		<m-table-edit
																			headers="ASC.ScholarshipSchedHeaders"
																			props="ASC.ScholarshipSchedProps"
																			data="ASC.ScholarshipSchedData"
																			inputs="ASC.ScholarshipSchedInputs"
																			on-edit-save="ASC.updateScholarshipSchedule">
																		</m-table-edit>
																	</uib-tab>
																</uib-tabset>
															</a-col>
														</a-row>
														<p class="alert alert-info">{{ASC.DiscountText.ACADEMIC_SCHOLARSHIP}}</p>
													</div>
												</div>
											</a-col>
										</a-row>

										<!-- Total Discounts Summary -->
										<a-row>
											<a-col size="12">
												<div class="panel panel-success">
													<div class="panel-heading">
														<h6 class="panel-title">
															<a-row>
																<a-col size="6">
																	<a-glyph icon="calculator"></a-glyph> Discount Summary
																</a-col>
																<a-col size="6" align="right">

																		<a-button size="sm" type="success outline" ng-click="ASC.ActiveTabIndex = 0">
																			View Fees
																		</a-button>
																		<a-button size="sm" type="success outline" ng-click="ASC.ActiveTabIndex = 1">
																			View Schedule
																		</a-button>
																</a-col>
															</a-row>


														</h6>
													</div>
													<div class="panel-body">
														<m-formgroup ng-model="ASC.Assessment.payment_plan_id" label="Payment Plan" type="'btn-group'" ng-disabled="!ASC.StudentObj.id" options="ASC.PaymentPlans"></m-formgroup>
														<a-row>
														<a-col size="6">
																<m-formgroup ng-model="ASC.Assessment.gross_tuition_fee"
																	label="Tuition Fee"
																	type="'display'"
																	class="text-success">
																</m-formgroup>
															</a-col>
															<a-col size="6">
																<m-formgroup ng-model="ASC.Assessment.total_discount"
																	label="Total Discounts Applied"
																	type="'display'"
																	class="text-success">
																</m-formgroup>
															</a-col>
														</a-row>

														<!-- Detailed Discount Breakdown -->
														<a-row ng-if="ASC.Assessment.discount_details && ASC.Assessment.discount_details.length > 0">
															<a-col size="12">
																<div class="table-responsive">
																	<table class="table table-striped">
																		<thead>
																			<tr>
																				<th>Discount Type</th>
																				<th class="text-right">Amount</th>
																				<th class="text-right">Percentage/Fixed</th>
																			</tr>
																		</thead>
																		<tbody>
																			<tr ng-repeat="discount in ASC.Assessment.discount_details" ng-class="{'danger': discount.is_warning}">
																				<td>{{discount.type}}</td>
																				<td class="text-right" ng-if="!discount.is_note">₱{{discount.amount | number:2}}</td>
																				<td class="text-right" ng-if="discount.is_note"></td>
																				<td class="text-right">
																					<span ng-if="discount.percentage">{{discount.percentage}}%</span>
																					<span ng-if="discount.fixed">₱{{discount.fixed | number:2}}</span>
																				</td>
																			</tr>
																		</tbody>
																		<tfoot>
																			<tr class="success">
																				<td><strong>Total Discounts</strong></td>
																				<td class="text-right"><strong>₱{{ASC.Assessment.total_discount | number:2}}</strong></td>
																				<td></td>
																			</tr>
																					<tr class="danger" ng-if="ASC.Assessment.discount_exceeds_tuition">
																						<td colspan="3" class="text-center">
																							<a-glyph icon="warning-sign"></a-glyph><strong>Oops!Total discounts exceed the Tuition Fee by ₱{{ASC.Assessment.discount_excess_amount  | number:2}}.</strong>
																							<br>
																							Please adjust the discounts to proceed with the assessment.
																						</td>
																					</tr>
																		</tfoot>
																	</table>
																</div>
															</a-col>
														</a-row>
														<a-button  type="danger outline" ng-click="ASC.cancelAssessment()" ng-disabled="!ASC.Assessment.student_id">
															Reset Assessment
														</a-button>
													</div>
												</div>
											</a-col>
										</a-row>
									</uib-tab>
								</uib-tabset>

							</a-col>
							<a-col size="4" class="right-panel">
								<div class="panel panel-default" >
									<div class="panel-heading">
										<a-row>
											<a-col>
												<h5 class="panel-title pull-left" style="    margin-top: 3px;font-weight: bold;">Summary</h5>
												<a-button size="xs" type="danger outline" class="pull-right"  ng-click="ASC.cancelAssessment()" ng-disabled="!ASC.Assessment.student_id">
													<a-glyph  icon="remove"></a-glyph>
												</a-button>
												<div class="clear-fix"></div>
											</a-col>
										</a-row>

									</div>
									<div class="panel-body"  style="height:40vh;overflow-y:auto"	>
										<div ng-if="!ASC.StudentObj.id">
											No info to display.
										</div>
										<div ng-if="ASC.Assessment.student_id">
											<label>Student</label>
											<div style="font-size:1.6rem">{{ASC.StudentObj.name}}</div>
											<br>


											<dl class="dl-horizontal" ng-if="ASC.Assessment.student_id">

												<dt>Incoming Level</dt>
												<dd>
													{{(ASC.YearLevels | filter:{id:ASC.Assessment.year_level_id})[0].description}}
													<span ng-if="!ASC.Assessment.year_level_id">( Select Level )</span>
												</dd>

												<dt>Payment Scheme</dt>
												<dd>{{(ASC.PaymentPlans | filter:{id:ASC.Assessment.payment_plan_id})[0].name}}</dd>
												<dt ng-repeat-start="item in ASC.TuitionData" ng-if="item.class === 'warning'">{{item.fee}}</dt>
												<dd ng-repeat-end ng-if="item.class === 'warning'">{{item.display_amount}}</dd>
											</dl>

											<div class="alert alert-danger" ng-if="ASC.Assessment.discount_exceeds_tuition">
												<a-glyph icon="warning-sign"></a-glyph> Discount exceeded by
												<strong>₱{{ASC.Assessment.discount_excess_amount  | number:2}}</strong>.

											</div>
										</div>
									</div>
									<div class="panel-footer" style="background-color:white">
										<div class="text-right">
											<label>Total Amount Due</label>
											<h3  style="margin-top:0px" class="text-success">₱{{ASC.Assessment.assessment_total | number:2}}</h3>
										</div>
										<a-button size="lg" type="success btn-block" ng-disabled="!ASC.Assessment.student_id || ASC.Assessment.assessment_total <= 0 || ASC.Assessment.discount_exceeds_tuition" ng-click="ASC.saveAssessment()">
											Save Assessment
										</a-button>
									</div>
								</div>
							</a-col>
						</a-row>

						<a-row>
							<a-col size="8">


							</a-col>
							<a-col size="4">

							</a-col>
						</a-row>
					</a-content>
				</a-canvas>
			</a-container>
		</a-module>
		<a-modal title="Student Assessment Requests" id="StudentAssessmentRequestModal" is-large="true" has-close="true">
			<a-modal-body>
				<a-row>
					<a-col size="2" align="left">
						<m-formgroup ng-model="ASC.FilterStatus" options="ASC.FilterStatusOptions" label="Status" hide-label="true" style="margin-right:-25px;"></m-formgroup>
					</a-col>
					<a-col size="1">
						<a-button type="primary" ng-click="ASC.filterRequests()" ng-disabled="ASC.isRequestsLoading">
							<a-glyph icon="filter"></a-glyph>
							Filter
						</a-button>
					</a-col>
					<a-col size="6"></a-col>
					<a-col size="3" align="right" style="margin-bottom:5px;">
						<a-pager meta="ASC.Meta" on-navigate="ASC.gotoPage"></a-pager>

					</a-col>
					<a-col>
						<a-table headers="ASC.RequestsHeaders" ng-model="ASC.ActiveRequest" props="ASC.RequestsProps" data="ASC.RequestsData" is-preload="ASC.isRequestsLoading"></a-table>
					</a-col>

					<a-col ng-show="ASC.ActiveRequest.id">
						<hr/>
						<a-row>

							<a-col size="5">
								<m-formgroup ng-model="ASC.ActiveRequest.student_name" label="Student Name"></m-formgroup>
							</a-col>

							<a-col size="3">
								<m-formgroup ng-model="ASC.ActiveRequest.incoming_year_level" label="Incoming Level" options="SMC.YearLevels" option-label="description" select-prefix="Select Incoming  Level"></m-formgroup>
							</a-col>
							<a-col size="4">
								<m-formgroup ng-model="ASC.ActiveRequest.payment_plan_id" label="Payment Plan" options="ASC.PaymentPlans"></m-formgroup>

							</a-col>
							<a-col size="8" style="margin-bottom:5px;" class="alert-request">
								<label>Action Required</label>
								<div class="alert alert-danger" ng-show="!ASC.ActiveRequest.ref_no && !ASC.isSearching">

											<span ng-show="!ASC.RegResults">
											Missing <b>Registration No.</b>. Click <a-glyph icon="search"></a-glyph> to search for the record.
											</span>
											<span ng-show="ASC.RegResults">
											Choose a <b>Registration No.</b> from the results dropdown.
											</span>
									</div>
									<div class="alert alert-danger" ng-show="!ASC.ActiveRequest.ref_no && ASC.isSearching">
										Please wait. Searching records for <b>{{ASC.searchWord}}</b> ...
									</div>
									<div class="alert alert-info" ng-show="ASC.RegResults[ASC.ActiveRequest.ref_no]">
										Record retrieved for <b>{{ASC.RegResults[ASC.ActiveRequest.ref_no].full_name}}</b> with status
										<b>{{ASC.RegResults[ASC.ActiveRequest.ref_no].status}}	</b>
									</div>
									<div class="alert alert-warning" ng-show="ASC.ActiveRequest.ref_no && !ASC.RegResults ">
										 Click <b> Assess</b> to begin. Click <a-glyph icon="search"></a-glyph> to search,  <b>Registration No.</b> is empty.
									</div>
							</a-col>
							<a-col size="4">
							<m-formgroup ng-model="ASC.ActiveRequest.textbook_option"
									label="Purchase Textbook & Supplies?"
									options="[
										{id:'COMPLETE', name:'Complete set'},
										{id:'SELECTED', name:'Selected items'},
										{id:'NONE', name:'No'}
									]"
									select-prefix="Select Option"
									ng-disabled="ASC.Assessment.purchase_textbooks_supplies === 'N'">
								</m-formgroup>
							</a-col>
						</a-row>
						<a-row>
						<a-col size="8"  >
								<div class="text-bold" style="display:inline-block;margin-bottom:5px;">
									Notes:
								</div>
								<div class="well">
									{{ASC.ActiveRequest.notes}}
								</div>
							</a-col>
							<a-col size="3">
								<m-formgroup ng-model="ASC.ActiveRequest.ref_no"  ng-disabled="ASC.isSearching" label="Registration No." options="ASC.RegNos" ></m-formgroup>
							</a-col>
							<a-col size="1" align="right">
								<div style="margin-top:25px">
									<a-button type="default outline" ng-disabled="!ASC.ActiveRequest.id || ASC.isSearching" ng-click="ASC.searchRegistration()">
										<a-glyph icon="search"></a-glyph>
									</a-button>
								</div>
							</a-col>


							<a-col size="4" align="right" style="margin-top:5px;">
								<a-button type="success"   size="lg"	ng-click="ASC.processAssessment(ASC.ActiveRequest)" ng-disabled="ASC.isProcessing ||!ASC.ActiveRequest.ref_no "> &nbsp;<a-glyph icon="pencil"></a-glyph>&nbsp;&nbsp;
									Assess
								&nbsp;&nbsp;&nbsp;</a-button>
								<a-button type="danger outline"  size="lg" ng-disabled="!ASC.ActiveRequest.id" ng-click="ASC.deleteRequest()"> <a-glyph icon="trash"></a-glyph>
								</a-button>
								<a-button type="default outline"  size="lg" ng-disabled="!ASC.ActiveRequest.id" ng-click="ASC.clearActiveRequest()"> <a-glyph icon="chevron-up"></a-glyph>
								</a-button>
							</a-col>
						</a-row>


					</a-col>
				</a-row>
			</a-modal-body>
		</a-modal>

		<a-modal title="Student Assessment {{!ASC.isConfirmed?'Confirmation':'Confirmed'}}"  ng-class="{'confirmed':ASC.isConfirmed}" id="StudentAssessmentConfirmationModal">
			<a-modal-body>
				<div ng-show="!ASC.isConfirmed">
				<label>Student</label>
				<div style="font-size:1.6rem">{{ASC.Assessment.summary_details.student_name}}</div>

				<br>

				<dl class="dl-horizontal assessment-summary-list" ng-if="ASC.Assessment.student_id ">
					<dt>School Year</dt>
					<dd>{{ASC.Assessment.esp}}- {{ASC.Assessment.esp+1}}</dd>
					<dt>Student Type</dt>
					<dd>
						{{ASC.Assessment.summary_details.student_type === 'NEW' ? 'New Student' : 'Old Student'}}
					</dd>
					<dt>Incoming Level</dt>
					<dd>
						{{ASC.Assessment.summary_details.year_level}}
						<span ng-if="!ASC.Assessment.summary_details.year_level">( Select Level )</span>
					</dd>

					<dt>Payment Scheme</dt>
					<dd>{{ASC.Assessment.summary_details.payment_plan}}</dd>

					<!-- Fee details section -->
					<dt>Total Tuition, Msc., and Other Fees</dt>
					<dd>₱{{ASC.Assessment.summary_details.total_tuition_misc_other | number:2}}</dd>

					<dt>Reservation Fee Applied</dt>
					<dd>₱{{ASC.Assessment.summary_details.reservation_fee_amount | number:2}}</dd>

					<dt>Discounts/Scholarships Applied</dt>
					<dd>₱{{ASC.Assessment.summary_details.total_discount | number:2}}</dd>

					<dt>Textbooks and School Supplies</dt>
					<dd>₱{{ASC.Assessment.summary_details.textbooks_supplies_total | number:2}}</dd>

					<dt class="text-success" style="font-weight: bold; margin-top: 15px;">Total Amount Due</dt>
					<dd class="text-success" style="font-weight: bold; font-size: 1.2em; margin-top: 15px;">₱{{ASC.Assessment.summary_details.assessment_total | number:2}}</dd>

					<!-- Payment schedule section -->
					<dt class="text-primary" style="margin-top: 15px; border-top: 1px solid #eee; padding-top: 10px;">Payment Schedule</dt>
					<dd class="text-primary" style="margin-top: 15px; border-top: 1px solid #eee; padding-top: 10px;"></dd>

					<dt ng-repeat-start="schedule in ASC.Assessment.summary_details.payment_schedules">{{schedule.schedule}}</dt>
					<dd ng-repeat-end>{{schedule.display_amount}}</dd>

					<!-- Discount details section -->
					<dt ng-if="ASC.Assessment.summary_details.discount_details.length > 0" class="text-danger" style="margin-top: 15px; border-top: 1px solid #eee; padding-top: 10px;">Discount Details</dt>
					<dd ng-if="ASC.Assessment.summary_details.discount_details.length > 0" class="text-danger" style="margin-top: 15px; border-top: 1px solid #eee; padding-top: 10px;"></dd>

					<dt ng-repeat-start="discount in ASC.Assessment.summary_details.discount_details" ng-class="{'text-warning': discount.adjusted, 'text-info': discount.is_note, 'text-danger': discount.is_warning}">{{discount.type}}</dt>
					<dd ng-repeat-end ng-class="{'text-warning': discount.adjusted, 'text-info': discount.is_note, 'text-danger': discount.is_warning}">
						<span ng-if="!discount.is_note">₱{{discount.amount | number:2}} ({{discount.apply_on}})</span>
						<span ng-if="discount.adjusted"><br><small>Original: ₱{{discount.original_amount | number:2}}</small></span>
					</dd>
				</dl>
			</div>
			<div ng-show="ASC.isConfirmed">
				<a-row>
					<a-col size="8">
						<label>Student</label>
						<div style="font-size:1.6rem">{{ASC.Assessment.summary_details.student_name}}</div>
					</a-col>
					<a-col size="4">
						<label>Assessment File</label>
						<a-button type="default outline" ng-show="ASC.isConfirmed && ASC.AssessmentID" ng-click="ASC.previewAttachment()" ng-disabled="ASC.isSending">
							<a-glyph icon="file"></a-glyph> Preview PDF
						</a-button>
					</a-col>
				</a-row>

				<br>
				<dl class="dl-horizontal assessment-summary-list" ng-if="ASC.Assessment.student_id ">
					<dt>Assessment No.</dt>
					<dd>{{ASC.AssessmentID}}
					</dd>
					<dt>School Year</dt>
					<dd>{{ASC.Assessment.esp}}- {{ASC.Assessment.esp+1}}</dd>
					<dt>Student Type</dt>
					<dd>
						{{ASC.Assessment.summary_details.student_type === 'NEW' ? 'New Student' : 'Old Student'}}
					</dd>
					<dt>Incoming Level</dt>
					<dd>
						{{ASC.Assessment.summary_details.year_level}}
						<span ng-if="!ASC.Assessment.summary_details.year_level">( Select Level )</span>
					</dd>

					<dt>Payment Scheme</dt>
					<dd>{{ASC.Assessment.summary_details.payment_plan}}</dd>

				</dl>
				<a-row>
					<a-col>
						<label> Emails</label>
						<div class="notify-email" ng-repeat="emailObj in ASC.EmailOptions">
							<div class="email-option">
								<m-formgroup label="Email"  type="'checkbox'"  hide-label="true" ng-model="ASC.SendEmailFlags[emailObj.key]"></m-formgroup>
								<div class="email-label">
									<label>{{emailObj.rel}} </label>	:
									<m-formgroup label="Email"  hide-label="true" ng-model="ASC.SendEmailList[emailObj.key]" ng-disabled="!ASC.SendEmailFlags[emailObj.key]"></m-formgroup>
								</div>
							</div>
						</div>
						<div class="alert alert-warning" ng-show="!ASC.isEmailValid">
							<a-glyph icon="exclamation-sign"></a-glyph> Please select at least one email recipient to send the assessment.
						</div>

					</a-col>
				</a-row>

			</div>

			</a-modal-body>
			<a-modal-footer>
				<a-row>
					<a-col size="4" align="left">
						<a-button type="default outline"  ng-show="!ASC.isConfirmed && !ASC.AssessmentID"   ng-click="ASC.closeAssessment()" ng-disabled="ASC.isConfirming || ASC.isSending">
							<a-glyph icon="remove"></a-glyph> Close Window
						</a-button>

						<a-button type="{{ASC.isEmailSent?'primary':'default outline'}}"  ng-show="ASC.isConfirmed && ASC.AssessmentID"   ng-click="ASC.doneAssessment()" ng-disabled="ASC.isConfirming || ASC.isSending || !ASC.isEmailSent">
							<a-glyph icon="ok"></a-glyph> Assessment OK
						</a-button>

					</a-col>
					<a-col size="8" align="right">
						<a-button type="primary outline" ng-show="!ASC.isConfirmed && !ASC.AssessmentID" ng-click="ASC.confirmAssessment()" ng-disabled="ASC.isConfirming ">
							<a-glyph icon="ok"></a-glyph>
							{{ASC.isConfirming?'Confirming':'Confirm'}} Assessment
						</a-button>
						<a-button type="default outline" ng-show="ASC.isConfirmed && ASC.AssessmentID" ng-click="ASC.previewConfirmation()" ng-disabled="ASC.isSending">
							<a-glyph icon="eye-open"></a-glyph> Preview Email
						</a-button>
						<a-button type="success outline" ng-show="ASC.isConfirmed && ASC.AssessmentID" ng-click="ASC.sendConfirmation()" ng-disabled="ASC.isSending || !ASC.isEmailValid">
							<a-glyph icon="envelope"></a-glyph>
							{{ASC.isSending?'Sending':'Send'}} Assessment
						</a-button>
					</a-col>
				</a-row>
			</a-modal-footer>
		</a-modal>
	</div>
</div>